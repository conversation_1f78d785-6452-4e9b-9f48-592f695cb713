import 'package:http/http.dart' as http;

class ApiConfig {
  static const String baseUrl = 'http://localhost:8000';
  static const String apiVersion = 'v1';

  // Debug mode - set to true to test without authentication
  static const bool debugMode = true;

  // Authentication endpoints
  static const String loginEndpoint = '/login';
  static const String registerEndpoint = '/register';

  // Transcription endpoints
  static const String uploadEndpoint = '/api/$apiVersion/transcription/upload';
  static const String submitEndpoint = '/api/$apiVersion/transcription/submit';
  static const String statusEndpoint = '/api/$apiVersion/transcription';
  static const String resultEndpoint = '/api/$apiVersion/transcription';
  static const String historyEndpoint =
      '/api/$apiVersion/transcription/history';

  // Streaming endpoints
  static const String streamingSessionEndpoint =
      '/api/$apiVersion/streaming/session';
  static const String streamingWebSocketEndpoint =
      '/api/$apiVersion/streaming/ws';
  static const String streamingActiveSessionsEndpoint =
      '/api/$apiVersion/streaming/sessions/active';

  // Timeout settings
  static const Duration connectTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 60);

  // Server status tracking
  static bool _isServerReachable = true;
  static DateTime? _lastServerCheck;
  static const Duration _serverCheckCooldown = Duration(minutes: 2);

  static bool get isServerReachable => _isServerReachable;
  static DateTime? get lastServerCheck => _lastServerCheck;

  /// Quick server reachability check with caching
  static Future<bool> checkServerStatus({bool forceCheck = false}) async {
    // Use cached result if recent
    if (!forceCheck &&
        _lastServerCheck != null &&
        DateTime.now().difference(_lastServerCheck!).inSeconds < 30) {
      return _isServerReachable;
    }

    try {
      final response = await http
          .get(Uri.parse('$baseUrl/health'))
          .timeout(const Duration(seconds: 5));

      _isServerReachable = response.statusCode == 200;
      _lastServerCheck = DateTime.now();

      if (_isServerReachable) {
        print('✅ Server is reachable');
      } else {
        print('⚠️ Server returned status: ${response.statusCode}');
      }

      return _isServerReachable;
    } catch (e) {
      _isServerReachable = false;
      _lastServerCheck = DateTime.now();
      print('❌ Server unreachable: $e');
      return false;
    }
  }

  /// Get appropriate error message based on server status
  static String getServerErrorMessage() {
    if (!_isServerReachable) {
      return 'Server is currently unavailable. Please try again later.';
    }
    return 'Request failed. Please check your connection and try again.';
  }

  /// Reset server status (useful for retries)
  static void resetServerStatus() {
    _lastServerCheck = null;
  }
}
