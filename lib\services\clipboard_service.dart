import 'dart:ffi';
import 'package:flutter/services.dart';
import 'package:win32/win32.dart';

class ClipboardService {
  // Copy text to clipboard using <PERSON><PERSON><PERSON>'s clipboard
  static Future<void> copyToClipboard(String text) async {
    try {
      await Clipboard.setData(ClipboardData(text: text));
    } catch (e) {
      throw Exception('Failed to copy to clipboard: $e');
    }
  }

  // Simulate Ctrl+V keypress to paste
  static Future<void> pasteFromClipboard() async {
    try {
      // Send Ctrl+V key combination using win32
      final user32 = DynamicLibrary.open('user32.dll');
      final keybd_event = user32.lookupFunction<
          Void Function(Int32, Int32, Int32, IntPtr),
          void Function(int, int, int, int)>('keybd_event');

      keybd_event(VK_CONTROL, 0, 0, 0);
      keybd_event(0x56, 0, 0, 0); // V key
      keybd_event(0x56, 0, KEYEVENTF_KEYUP, 0); // V key up
      keybd_event(VK_CONTROL, 0, KEYEVENTF_KEYUP, 0);
    } catch (e) {
      throw Exception('Failed to paste from clipboard: $e');
    }
  }

  // Copy text and paste it immediately
  static Future<void> copyAndPaste(String text) async {
    await copyToClipboard(text);
    await Future.delayed(const Duration(milliseconds: 100));
    await pasteFromClipboard();
  }
}
