import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';
import 'dart:math';
import 'package:http/http.dart' as http;
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:record/record.dart';
import 'api_config.dart';
import 'auth_service.dart';
import 'rate_limit_detector.dart';
import 'audio_service.dart';

class StreamingOptions {
  final int sampleRate;
  final String encoding;
  final int channels;
  final bool interimResults;
  final bool punctuate;
  final bool formatText;
  final bool speakerLabels;
  final String languageCode;
  final bool redactPii;
  final double? maxDurationMinutes; // Add max duration to limit cost
  final int? estimatedCost; // Add estimated cost override

  StreamingOptions({
    this.sampleRate = 16000,
    this.encoding = 'pcm_s16le',
    this.channels = 1,
    this.interimResults = true,
    this.punctuate = true,
    this.formatText = true,
    this.speakerLabels = false,
    this.languageCode = 'en',
    this.redactPii = false,
    this.maxDurationMinutes = 0.1, // Default to 6 seconds = 1 credit
    this.estimatedCost = 1, // Default to 1 credit minimum
  });

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      'sample_rate': sampleRate,
      'encoding': encoding,
      'channels': channels,
      'interim_results': interimResults,
      'punctuate': punctuate,
      'format_text': formatText,
      'speaker_labels': speakerLabels,
      'language_code': languageCode,
      'redact_pii': redactPii,
    };

    // Add cost control parameters
    if (maxDurationMinutes != null) {
      json['max_duration_minutes'] = maxDurationMinutes!;
    }
    if (estimatedCost != null) {
      json['estimated_cost'] = estimatedCost!;
      json['cost_override'] = estimatedCost!;
      json['credit_limit'] = estimatedCost!;
    }

    return json;
  }
}

class StreamingTranscriptionService {
  WebSocketChannel? _channel;
  StreamController<Map<String, dynamic>>? _transcriptionController;
  String? _sessionId;
  bool _isStreaming = false;

  // Audio recording related variables
  AudioRecorder? _audioRecorder;
  StreamSubscription<Uint8List>? _audioSubscription;

  Stream<Map<String, dynamic>>? get transcriptionStream =>
      _transcriptionController?.stream;

  bool get isStreaming => _isStreaming;
  String? get sessionId => _sessionId;

  // Create streaming session with retry logic
  Future<Map<String, dynamic>> createSession({
    StreamingOptions? options,
    int maxRetries = 3,
  }) async {
    int retryCount = 0;

    while (retryCount < maxRetries) {
      try {
        final token = await AuthService.instance.getAccessToken();
        if (token == null) {
          return {'success': false, 'error': 'Not authenticated'};
        }

        // Test connectivity first if this is the first attempt
        if (retryCount == 0) {
          print('🔍 Testing server connectivity before creating session...');
          final connectTest = await _testServerConnectivity();
          if (!connectTest) {
            return {
              'success': false,
              'error':
                  'Cannot connect to server. Please check if the server is running.',
            };
          }
        }

        print(
          '🏗️ Creating streaming session (attempt ${retryCount + 1}/$maxRetries)...',
        );

        final requestBody = {
          'access_token': token,
          'options': {
            ...(options?.toJson() ?? StreamingOptions().toJson()),
            'max_duration_minutes':
                0.1, // Limit session to 6 seconds = 1 credit
            'estimated_cost': 1, // Force 1 credit requirement
          },
        };

        print('📤 Request body being sent: ${jsonEncode(requestBody)}');

        // Use AuthService's authenticatedRequest which handles token refresh automatically
        final response = await AuthService.instance
            .authenticatedRequest(
              'POST',
              ApiConfig.streamingSessionEndpoint,
              body: requestBody,
            )
            .timeout(
              const Duration(seconds: 10),
              onTimeout: () {
                throw Exception(
                  'Request timeout - server took too long to respond',
                );
              },
            );

        print('📋 Session creation response: ${response.statusCode}');

        // Log response body for debugging
        if (response.body.isNotEmpty) {
          print('📋 Response body: ${response.body}');
        }

        if (response.statusCode == 201 || response.statusCode == 200) {
          final data = jsonDecode(response.body);
          _sessionId = data['session_id'];
          print('✅ Streaming session created: $_sessionId');
          return {'success': true, 'data': data};
        } else {
          final errorMessage = _parseErrorResponse(response);

          // Handle rate limiting
          if (response.statusCode == 429) {
            RateLimitDetector.instance.recordRateLimit(
              rateLimitType: 'api_request',
              rateLimitDetails: response.body,
            );
            return {
              'success': false,
              'error': 'Server is busy (rate limited). Too many requests.',
            };
          }

          // Don't retry for authentication errors
          if (response.statusCode == 401) {
            return {
              'success': false,
              'error': 'Authentication failed. Please log in again.',
            };
          }

          throw Exception(errorMessage);
        }
      } catch (e) {
        retryCount++;
        print(
          '❌ Session creation failed (attempt $retryCount/$maxRetries): $e',
        );

        if (retryCount >= maxRetries) {
          String errorMessage =
              'Failed to create streaming session after $maxRetries attempts';

          if (e.toString().contains('timeout')) {
            errorMessage = 'Server connection timeout. Please try again.';
          } else if (e.toString().contains('connection') ||
              e.toString().contains('network')) {
            errorMessage =
                'Network connection error. Please check your internet connection.';
          } else if (e.toString().contains('SocketException')) {
            errorMessage =
                'Cannot connect to server. Please ensure the server is running.';
          }

          return {'success': false, 'error': errorMessage};
        } else {
          // Wait before retrying
          final delaySeconds = (retryCount * 2).clamp(1, 5);
          print('⏳ Retrying in $delaySeconds seconds...');
          await Future.delayed(Duration(seconds: delaySeconds));
        }
      }
    }

    return {'success': false, 'error': 'Unexpected error occurred'};
  }

  // Test server connectivity
  Future<bool> _testServerConnectivity() async {
    try {
      final response = await http
          .get(Uri.parse('${ApiConfig.baseUrl}/health'))
          .timeout(const Duration(seconds: 5));

      return response.statusCode == 200;
    } catch (e) {
      print('❌ Server connectivity test failed: $e');
      return false;
    }
  }

  // Parse error response
  String _parseErrorResponse(http.Response response) {
    try {
      final error = jsonDecode(response.body);
      return error['detail'] ?? error['message'] ?? 'Session creation failed';
    } catch (e) {
      return 'Session creation failed (${response.statusCode})';
    }
  }

  // Start streaming transcription
  Future<Map<String, dynamic>> startStreaming() async {
    try {
      if (_sessionId == null) {
        return {'success': false, 'error': 'No session created'};
      }

      final token = await AuthService.instance.getAccessToken();
      if (token == null) {
        return {'success': false, 'error': 'Not authenticated'};
      }

      // Connect to WebSocket through FastAPI server
      final wsUrl =
          '${ApiConfig.baseUrl.replaceAll('http', 'ws')}${ApiConfig.streamingWebSocketEndpoint}/$_sessionId?token=$token';

      print('🔌 Connecting to FastAPI WebSocket: $wsUrl');

      _channel = WebSocketChannel.connect(Uri.parse(wsUrl));
      _transcriptionController =
          StreamController<Map<String, dynamic>>.broadcast();

      // Listen for messages
      _channel!.stream.listen(
        (message) {
          try {
            print('📨 Received WebSocket message: $message');
            final data = jsonDecode(message);
            _transcriptionController?.add(data);
          } catch (e) {
            print('❌ Error parsing WebSocket message: $e');
            print('📨 Raw message was: $message');
          }
        },
        onError: (error) {
          print('❌ WebSocket error: $error');
          _transcriptionController?.addError(error);
        },
        onDone: () {
          print('🔌 WebSocket connection closed by server');
          _stopStreaming();
        },
      );

      // Connection established, no need to send test message
      print('✅ FastAPI WebSocket connected successfully');

      _isStreaming = true;
      return {'success': true, 'message': 'Streaming started'};
    } catch (e) {
      return {'success': false, 'error': 'Failed to start streaming: $e'};
    }
  }

  // Stop streaming
  Future<void> stopStreaming() async {
    await _stopStreaming();
  }

  Future<void> _stopStreaming() async {
    _isStreaming = false;

    // Stop audio capture first
    await _stopAudioCapture();

    // Send stop command only if channel is still active
    if (_channel != null) {
      try {
        // Only send stop command if the connection is still open
        _channel!.sink.add(jsonEncode({'type': 'stop'}));
      } catch (e) {
        print('⚠️ Could not send stop command (connection already closed): $e');
      }

      try {
        await _channel!.sink.close();
      } catch (e) {
        print('⚠️ Error closing WebSocket: $e');
      }

      _channel = null;
    }

    try {
      await _transcriptionController?.close();
    } catch (e) {
      print('⚠️ Error closing transcription controller: $e');
    }

    _transcriptionController = null;
    _sessionId = null;
  }

  // Pause streaming
  Future<void> pauseStreaming() async {
    if (_channel != null && _isStreaming) {
      _channel!.sink.add(jsonEncode({'type': 'pause'}));
    }
  }

  // Resume streaming
  Future<void> resumeStreaming() async {
    if (_channel != null && _isStreaming) {
      _channel!.sink.add(jsonEncode({'type': 'resume'}));
    }
  }

  // Calculate RMS amplitude from PCM 16-bit audio data (0.0 to 1.0) with voice-optimized filtering
  double _calculateAudioLevel(Uint8List chunk) {
    if (chunk.length < 2) return 0.0;

    // Convert to samples first
    List<double> samples = [];
    for (int i = 0; i < chunk.length - 1; i += 2) {
      int sample = (chunk[i + 1] << 8) | chunk[i];
      if (sample > 32767) sample -= 65536; // Convert to signed
      samples.add(sample / 32768.0); // Normalize to -1.0 to 1.0
    }

    if (samples.isEmpty) return 0.0;

    // Apply high-pass filter to reduce low-frequency background noise (< 80Hz)
    List<double> filteredSamples = _applyHighPassFilter(samples, 16000, 80.0);

    // Apply voice-range emphasis (200Hz - 3000Hz where most speech energy is)
    filteredSamples = _applyVoiceEmphasis(filteredSamples, 16000);

    // Calculate RMS of filtered signal
    double sum = 0.0;
    for (double sample in filteredSamples) {
      sum += sample * sample;
    }

    double rms = sqrt(sum / filteredSamples.length);

    // Voice-optimized scaling with adaptive threshold
    // More aggressive scaling for voice frequencies, less for background noise
    double voiceLevel = (rms * 6.0).clamp(0.0, 1.0);

    // Apply adaptive threshold - higher sensitivity for voice-like patterns
    if (voiceLevel < 0.03) {
      return 0.0; // Filter out low-level background noise
    }

    // Enhance mid-to-high frequency content (where voice clarity is)
    voiceLevel = pow(
      voiceLevel,
      0.7,
    ).toDouble(); // Slight compression for better response

    return voiceLevel.clamp(0.0, 1.0);
  }

  // Simple high-pass filter to remove low-frequency background noise
  List<double> _applyHighPassFilter(
    List<double> samples,
    double sampleRate,
    double cutoffFreq,
  ) {
    // Simple first-order high-pass filter
    double RC = 1.0 / (2.0 * pi * cutoffFreq);
    double dt = 1.0 / sampleRate;
    double alpha = RC / (RC + dt);

    List<double> filtered = List.filled(samples.length, 0.0);
    double prevInput = 0.0;
    double prevOutput = 0.0;

    for (int i = 0; i < samples.length; i++) {
      filtered[i] = alpha * (prevOutput + samples[i] - prevInput);
      prevInput = samples[i];
      prevOutput = filtered[i];
    }

    return filtered;
  }

  // Emphasize voice frequency range (200Hz - 3000Hz)
  List<double> _applyVoiceEmphasis(List<double> samples, double sampleRate) {
    // Simple band-pass emphasis for voice frequencies
    // This is a simplified approach - in practice you'd use proper FFT filtering
    List<double> emphasized = List.from(samples);

    // Apply slight amplification to mid-frequencies and gentle roll-off for very high frequencies
    for (int i = 1; i < emphasized.length - 1; i++) {
      // Simple derivative filter to emphasize transitions (voice characteristics)
      double derivative = emphasized[i + 1] - emphasized[i - 1];
      emphasized[i] = emphasized[i] + (derivative * 0.3); // Moderate emphasis
    }

    return emphasized;
  }

  // Send audio data to WebSocket (as binary)
  void sendAudioData(Uint8List audioData) {
    if (_channel != null && _isStreaming) {
      try {
        _channel!.sink.add(audioData);
        // print('📤 Sent ${audioData.length} bytes of audio data');
      } catch (e) {
        print('❌ Failed to send audio data: $e');
      }
    }
  }

  // Start capturing and streaming audio
  Future<void> startAudioCapture() async {
    try {
      _audioRecorder = AudioRecorder();

      // Check microphone permission
      if (!await _audioRecorder!.hasPermission()) {
        throw Exception('Microphone permission denied');
      }

      print('🎤 Starting audio capture for streaming...');

      // Configure for streaming (16kHz, 16-bit, mono)
      const config = RecordConfig(
        encoder: AudioEncoder.pcm16bits,
        sampleRate: 16000,
        numChannels: 1,
        bitRate: 256000,
      );

      // Start streaming audio
      final stream = await _audioRecorder!.startStream(config);

      // Process audio in 50ms chunks (1600 bytes = 800 samples * 2 bytes)
      const chunkSize = 1600;
      List<int> buffer = [];

      _audioSubscription = stream.listen(
        (chunk) {
          // Only process if still streaming
          if (!_isStreaming) return;

          // Calculate and emit audio level for waveform visualization
          final audioLevel = _calculateAudioLevel(chunk);
          // Emit to AudioService stream so SiriWaveformButton can react
          print('🎵 Streaming service audio level: $audioLevel');
          AudioService.emitAudioLevel(audioLevel);

          // Accumulate audio data
          buffer.addAll(chunk);

          // Send chunks when we have enough data
          while (buffer.length >= chunkSize) {
            final chunkToSend = buffer.take(chunkSize).toList();
            buffer = buffer.skip(chunkSize).toList();

            // Send audio data to FastAPI WebSocket
            sendAudioData(Uint8List.fromList(chunkToSend));
          }
        },
        onError: (error) {
          print('❌ Audio capture error: $error');
        },
        onDone: () {
          print('🎤 Audio capture stopped');
        },
      );

      print('✅ Audio capture started successfully');
    } catch (e) {
      print('❌ Failed to start audio capture: $e');
      rethrow;
    }
  }

  // Stop audio capture
  Future<void> _stopAudioCapture() async {
    try {
      if (_audioSubscription != null) {
        await _audioSubscription!.cancel();
        _audioSubscription = null;
        print('🎤 Audio subscription cancelled');
      }

      if (_audioRecorder != null) {
        await _audioRecorder!.stop();
        _audioRecorder = null;
        print('🎤 Audio recorder stopped');
      }

      // Reset audio level to zero when streaming stops
      AudioService.emitAudioLevel(0.0);
    } catch (e) {
      print('❌ Failed to stop audio capture: $e');
    }
  }

  // Get active sessions
  static Future<Map<String, dynamic>> getActiveSessions() async {
    try {
      // Use AuthService's authenticatedRequest which handles token refresh
      final response = await AuthService.instance.authenticatedRequest(
        'GET',
        ApiConfig.streamingActiveSessionsEndpoint,
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return {'success': true, 'data': data};
      } else {
        final error = jsonDecode(response.body);
        return {
          'success': false,
          'error': error['detail'] ?? 'Failed to get active sessions',
        };
      }
    } catch (e) {
      return {'success': false, 'error': 'Network error: $e'};
    }
  }

  // Terminate session
  Future<Map<String, dynamic>> terminateSession() async {
    if (_sessionId == null) {
      return {'success': false, 'error': 'No active session'};
    }

    try {
      final token = await AuthService.instance.getAccessToken();

      // Use AuthService's authenticatedRequest which handles token refresh
      final response = await AuthService.instance.authenticatedRequest(
        'DELETE',
        '${ApiConfig.streamingSessionEndpoint}/$_sessionId?token=$token',
      );

      await _stopStreaming();

      if (response.statusCode == 200) {
        return {'success': true, 'message': 'Session terminated'};
      } else {
        final error = jsonDecode(response.body);
        return {
          'success': false,
          'error': error['detail'] ?? 'Failed to terminate session',
        };
      }
    } catch (e) {
      return {'success': false, 'error': 'Network error: $e'};
    }
  }

  // Dispose resources
  void dispose() async {
    await _stopStreaming();
  }
}
