import 'dart:async';
import 'package:win32/win32.dart';
import 'package:flutter/foundation.dart';

class GlobalHotkeyService {
  static GlobalHotkeyService? _instance;
  static GlobalHotkeyService get instance =>
      _instance ??= GlobalHotkeyService._();

  GlobalHotkeyService._();

  bool _isListening = false;
  bool _wasKeyPressed = false;
  VoidCallback? _onKeyPressed;
  VoidCallback? _onKeyReleased;
  Timer? _keyStateTimer;

  // Configurable hotkeys
  List<String> _primaryHotkey = ['Ctrl', 'Win'];
  List<String> _secondaryHotkey = ['Alt', 'F1'];

  // Debouncing variables
  int _consecutivePressedCount = 0;
  int _consecutiveReleasedCount = 0;
  static const int _debounceThreshold = 3; // Require 3 consecutive readings

  // Map key names to virtual key codes
  Map<String, int> _keyCodeMap = {
    'Ctrl': VK_CONTROL,
    'Alt': VK_MENU,
    'Shift': VK_SHIFT,
    'Win': VK_LWIN,
    'A': 0x41,
    'B': 0x42,
    'C': 0x43,
    'D': 0x44,
    'E': 0x45,
    'F': 0x46,
    'G': 0x47,
    'H': 0x48,
    'I': 0x49,
    'J': 0x4A,
    'K': 0x4B,
    'L': 0x4C,
    'M': 0x4D,
    'N': 0x4E,
    'O': 0x4F,
    'P': 0x50,
    'Q': 0x51,
    'R': 0x52,
    'S': 0x53,
    'T': 0x54,
    'U': 0x55,
    'V': 0x56,
    'W': 0x57,
    'X': 0x58,
    'Y': 0x59,
    'Z': 0x5A,
    'F1': VK_F1,
    'F2': VK_F2,
    'F3': VK_F3,
    'F4': VK_F4,
    'F5': VK_F5,
    'F6': VK_F6,
    'F7': VK_F7,
    'F8': VK_F8,
    'F9': VK_F9,
    'F10': VK_F10,
    'F11': VK_F11,
    'F12': VK_F12,
    'Space': VK_SPACE,
    'Tab': VK_TAB,
    'Enter': VK_RETURN,
    'Escape': VK_ESCAPE,
  };

  // Update hotkey configuration
  void updateHotkeys(List<String> primaryHotkey, List<String> secondaryHotkey) {
    _primaryHotkey = List.from(primaryHotkey);
    _secondaryHotkey = List.from(secondaryHotkey);
    print(
      '🔧 Updated hotkeys: Primary: ${_primaryHotkey.join('+')} | Secondary: ${_secondaryHotkey.join('+')}',
    );
  }

  // Register the global hotkey
  Future<bool> registerHotkey({
    required VoidCallback onKeyPressed,
    required VoidCallback onKeyReleased,
    List<String>? primaryHotkey,
    List<String>? secondaryHotkey,
  }) async {
    if (_isListening) {
      return true;
    }

    _onKeyPressed = onKeyPressed;
    _onKeyReleased = onKeyReleased;

    // Update hotkeys if provided
    if (primaryHotkey != null) _primaryHotkey = List.from(primaryHotkey);
    if (secondaryHotkey != null) _secondaryHotkey = List.from(secondaryHotkey);

    try {
      // Start monitoring key state with a timer (increased interval for stability)
      _keyStateTimer = Timer.periodic(
        const Duration(
          milliseconds: 100,
        ), // Check every 100ms for more stability
        _checkKeyState,
      );

      _isListening = true;
      print(
        '✅ Global hotkey listener started: Primary: ${_primaryHotkey.join('+')} | Secondary: ${_secondaryHotkey.join('+')}',
      );
      return true;
    } catch (e) {
      print('❌ Failed to start global hotkey listener: $e');
      return false;
    }
  }

  // Unregister the global hotkey
  Future<void> unregisterHotkey() async {
    if (!_isListening) return;

    try {
      _keyStateTimer?.cancel();
      _keyStateTimer = null;
      _isListening = false;
      _wasKeyPressed = false;
      _consecutivePressedCount = 0;
      _consecutiveReleasedCount = 0;
      print('✅ Global hotkey listener stopped');
    } catch (e) {
      print('❌ Failed to stop global hotkey listener: $e');
    }
  }

  bool get isRegistered => _isListening;
  bool get isKeyPressed => _wasKeyPressed;

  // Check if a specific key is pressed
  bool _isKeyPressed(String keyName) {
    final keyCode = _keyCodeMap[keyName];
    if (keyCode == null) {
      print('⚠️ Unknown key: $keyName');
      return false;
    }

    // Special handling for Win key (check both left and right)
    if (keyName == 'Win') {
      return (GetAsyncKeyState(VK_LWIN) & 0x8000) != 0 ||
          (GetAsyncKeyState(VK_RWIN) & 0x8000) != 0;
    }

    return (GetAsyncKeyState(keyCode) & 0x8000) != 0;
  }

  // Check if a hotkey combination is pressed
  bool _isHotkeyPressed(List<String> hotkey) {
    if (hotkey.isEmpty) return false;

    for (final key in hotkey) {
      if (!_isKeyPressed(key)) {
        return false;
      }
    }
    return true;
  }

  // Check key state periodically with debouncing
  void _checkKeyState(Timer timer) {
    try {
      // Check if either primary or secondary hotkey is pressed
      final isPrimaryPressed = _isHotkeyPressed(_primaryHotkey);
      final isSecondaryPressed = _isHotkeyPressed(_secondaryHotkey);
      final isCurrentlyPressed = isPrimaryPressed || isSecondaryPressed;

      // Determine which hotkey is pressed for logging
      String pressedHotkey = '';
      if (isPrimaryPressed)
        pressedHotkey = _primaryHotkey.join('+');
      else if (isSecondaryPressed)
        pressedHotkey = _secondaryHotkey.join('+');

      // Implement debouncing logic
      if (isCurrentlyPressed) {
        _consecutivePressedCount++;
        _consecutiveReleasedCount = 0; // Reset release counter

        // Show debouncing progress for debugging
        if (_consecutivePressedCount == 1 && !_wasKeyPressed) {
          print(
            '🔍 Hotkey detected: $pressedHotkey (debouncing... ${_consecutivePressedCount}/$_debounceThreshold)',
          );
        } else if (_consecutivePressedCount < _debounceThreshold &&
            !_wasKeyPressed) {
          print(
            '🔍 Hotkey still pressed (debouncing... ${_consecutivePressedCount}/$_debounceThreshold)',
          );
        }

        // Only trigger if we have enough consecutive pressed readings and weren't already pressed
        if (_consecutivePressedCount >= _debounceThreshold && !_wasKeyPressed) {
          _wasKeyPressed = true;
          print(
            '🔥 Hotkey pressed: $pressedHotkey (confirmed after $_consecutivePressedCount readings)',
          );
          print('🔗 About to call _onKeyPressed callback...');
          _onKeyPressed?.call();
          print('✅ _onKeyPressed callback called');
        }
      } else {
        _consecutiveReleasedCount++;
        _consecutivePressedCount = 0; // Reset press counter

        // Show debouncing progress for debugging
        if (_consecutiveReleasedCount == 1 && _wasKeyPressed) {
          print(
            '🔍 Hotkey release detected (debouncing... ${_consecutiveReleasedCount}/$_debounceThreshold)',
          );
        } else if (_consecutiveReleasedCount < _debounceThreshold &&
            _wasKeyPressed) {
          print(
            '🔍 Hotkey still released (debouncing... ${_consecutiveReleasedCount}/$_debounceThreshold)',
          );
        }

        // Only trigger if we have enough consecutive released readings and were previously pressed
        if (_consecutiveReleasedCount >= _debounceThreshold && _wasKeyPressed) {
          _wasKeyPressed = false;
          _onKeyReleased?.call();
          print(
            '🔥 Hotkey released (confirmed after $_consecutiveReleasedCount readings)',
          );
        }
      }
    } catch (e) {
      print('❌ Error checking key state: $e');
    }
  }
}
