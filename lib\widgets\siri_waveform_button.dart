import 'package:flutter/material.dart';
import 'package:siri_wave/siri_wave.dart';
import '../services/audio_service.dart';
import 'dart:async';
import 'dart:math';

class SiriWaveformButton extends StatefulWidget {
  final VoidCallback? onTap;
  final Color color;
  final bool isRecording;
  final bool isStreaming;
  final bool isProcessing;
  final double size;

  const SiriWaveformButton({
    super.key,
    this.onTap,
    required this.color,
    this.isRecording = false,
    this.isStreaming = false,
    this.isProcessing = false,
    this.size = 120.0,
  });

  @override
  State<SiriWaveformButton> createState() => _SiriWaveformButtonState();
}

class _SiriWaveformButtonState extends State<SiriWaveformButton>
    with TickerProviderStateMixin {
  late IOS9SiriWaveformController _controller;
  StreamSubscription<double>? _audioLevelSubscription;

  // Animation controllers for transitions
  late AnimationController _waveformVisibilityController;
  late AnimationController _startButtonController;
  late AnimationController _breathingController;

  // Animations
  late Animation<double> _waveformOpacity;
  late Animation<double> _waveformScale;
  late Animation<double> _startButtonOpacity;
  late Animation<double> _startButtonScale;
  late Animation<double> _breathingAnimation;

  bool _showWaveform = false;

  @override
  void initState() {
    super.initState();

    // Initialize animation controllers
    _waveformVisibilityController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _startButtonController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _breathingController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    // Initialize animations
    _waveformOpacity = CurvedAnimation(
      parent: _waveformVisibilityController,
      curve: Curves.easeInOut,
    );

    _waveformScale = Tween<double>(begin: 0.3, end: 1.0).animate(
      CurvedAnimation(
        parent: _waveformVisibilityController,
        curve: Curves.elasticOut,
      ),
    );

    _startButtonOpacity = CurvedAnimation(
      parent: _startButtonController,
      curve: Curves.easeInOut,
    );

    _startButtonScale = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _startButtonController, curve: Curves.elasticOut),
    );

    // Breathing animation for idle state
    _breathingAnimation = Tween<double>(begin: 0.1, end: 0.25).animate(
      CurvedAnimation(parent: _breathingController, curve: Curves.easeInOut),
    );

    // Initialize the waveform controller
    _controller = IOS9SiriWaveformController(
      amplitude: 0.3,
      color1: const Color(0xFF007AFF), // iOS Blue
      color2: const Color(0xFF34C759), // iOS Green
      color3: const Color(0xFFFF3B30), // iOS Red
      speed: 0.15,
    );

    // Set initial state
    _updateVisibilityState();

    // Start breathing animation for idle state
    _breathingController.repeat(reverse: true);

    // Listen to audio level stream for real-time waveform updates
    _audioLevelSubscription = AudioService.audioLevelStream.listen(
      (level) {
        if (mounted && _showWaveform) {
          // Enhanced voice-focused amplitude response with improved filtering
          if (level < 0.02) {
            // Use subtle breathing animation for very low levels
            return;
          }

          // Voice-optimized scaling with better high-frequency response
          final scaledLevel = (level * 3.5).clamp(0.2, 0.9);
          _controller.amplitude = scaledLevel;
        }
      },
      onError: (error) {
        print('❌ Audio level stream error: $error');
      },
    );

    // Listen to breathing animation when waveform is visible but not actively recording
    _breathingAnimation.addListener(() {
      if (mounted &&
          _showWaveform &&
          !widget.isRecording &&
          !widget.isStreaming) {
        _controller.amplitude = _breathingAnimation.value;
      }
    });
  }

  @override
  void didUpdateWidget(SiriWaveformButton oldWidget) {
    super.didUpdateWidget(oldWidget);
    _updateVisibilityState();
  }

  void _updateVisibilityState() {
    final shouldShowWaveform =
        widget.isRecording || widget.isStreaming || widget.isProcessing;

    if (shouldShowWaveform != _showWaveform) {
      setState(() {
        _showWaveform = shouldShowWaveform;
      });

      if (_showWaveform) {
        // Hide start button first, then show waveform
        _startButtonController.reverse().then((_) {
          _waveformVisibilityController.forward();
        });
      } else {
        // Hide waveform first, then show start button
        _waveformVisibilityController.reverse().then((_) {
          _startButtonController.forward();
        });
      }
    }

    // Start the start button animation if we should show it initially
    if (!_showWaveform && _startButtonController.value == 0.0) {
      _startButtonController.forward();
    }
  }

  @override
  void dispose() {
    _audioLevelSubscription?.cancel();
    _waveformVisibilityController.dispose();
    _startButtonController.dispose();
    _breathingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.size * 2.5,
      height: widget.size * 1.5,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Waveform (visible during recording/streaming)
          AnimatedBuilder(
            animation: _waveformVisibilityController,
            builder: (context, child) {
              return Opacity(
                opacity: _waveformOpacity.value,
                child: Transform.scale(
                  scale: _waveformScale.value,
                  child: Container(
                    width: widget.size * 2.5,
                    height: widget.size * 1.5,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20),
                      color: Colors.transparent,
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(18),
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          // Waveform visualization
                          SiriWaveform.ios9(
                            controller: _controller,
                            options: IOS9SiriWaveformOptions(
                              height: widget.size * 1.4,
                              width: widget.size * 2.4,
                              showSupportBar: true,
                            ),
                          ),
                          // Center icon overlay for waveform
                          _buildWaveformCenterIcon(),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            },
          ),

          // Start button (visible when not recording/streaming)
          AnimatedBuilder(
            animation: _startButtonController,
            builder: (context, child) {
              return Opacity(
                opacity: _startButtonOpacity.value,
                child: Transform.scale(
                  scale: _startButtonScale.value,
                  child: _buildStartButton(),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildStartButton() {
    return GestureDetector(
      onTap: widget.onTap,
      child: Container(
        width: widget.size * 0.8,
        height: widget.size * 0.8,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              widget.color.withOpacity(0.8),
              widget.color,
              widget.color.withOpacity(0.9),
            ],
          ),
          boxShadow: [
            BoxShadow(
              color: widget.color.withOpacity(0.3),
              blurRadius: 20,
              spreadRadius: 2,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(widget.size * 0.4),
            onTap: widget.onTap,
            child: Center(
              child: Icon(
                Icons.mic,
                color: Colors.white,
                size: widget.size * 0.25,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildWaveformCenterIcon() {
    IconData iconData;

    if (widget.isProcessing) {
      iconData = Icons.hourglass_empty;
    } else if (widget.isRecording || widget.isStreaming) {
      iconData = Icons.stop;
    } else {
      iconData = Icons.mic;
    }

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Colors.black.withOpacity(0.1),
      ),
      child: Icon(
        iconData,
        color: Colors.white.withOpacity(0.8),
        size: widget.size * 0.15,
      ),
    );
  }
}
