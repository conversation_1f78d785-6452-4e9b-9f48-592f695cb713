import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/history_provider.dart';
import '../models/transcription_history.dart';
import '../services/clipboard_service.dart';
import '../utils/timezone_utils.dart';
import 'dart:async';

class HistoryScreen extends StatefulWidget {
  const HistoryScreen({super.key});

  @override
  State<HistoryScreen> createState() => _HistoryScreenState();
}

class _HistoryScreenState extends State<HistoryScreen> {
  final ScrollController _scrollController = ScrollController();
  Timer? _autoRefreshTimer;

  // Track expanded state for each history item
  final Set<String> _expandedItems = <String>{};

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Load history from cache only - no server request unless cache is empty
      Provider.of<HistoryProvider>(context, listen: false).loadHistory(
        refresh: false,
        afterTranscription: false,
      ); // Cache-only approach
    });

    // Start auto-refresh timer to check for updates every 30 seconds
    _startAutoRefresh();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _autoRefreshTimer?.cancel();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent) {
      Provider.of<HistoryProvider>(context, listen: false).loadMoreHistory();
    }
  }

  void _startAutoRefresh() {
    _autoRefreshTimer = Timer.periodic(Duration(seconds: 30), (timer) {
      final provider = Provider.of<HistoryProvider>(context, listen: false);
      // Check if we have any processing items or if cache is old, then refresh
      if (provider.historyItems.any((item) => item.isProcessing)) {
        print('🔄 Auto-refresh: Found processing items, refreshing history');
        provider.loadHistory(refresh: true, forceServer: true);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Scaffold(
      backgroundColor: colorScheme.surface,
      body: Consumer<HistoryProvider>(
        builder: (context, provider, child) {
          return RefreshIndicator(
            onRefresh: () => provider.refreshHistory(),
            child: Column(
              children: [
                // Header with cache info
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: colorScheme.surface,
                    border: Border(
                      bottom: BorderSide(color: colorScheme.outline, width: 1),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.history,
                        color: colorScheme.onSurface,
                        size: 24,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Transcription History',
                              style: Theme.of(context).textTheme.headlineSmall
                                  ?.copyWith(color: colorScheme.onSurface),
                            ),
                            if (provider.totalItems > 0) ...[
                              const SizedBox(height: 2),
                              Row(
                                children: [
                                  Text(
                                    '${provider.totalItems} total transcriptions',
                                    style: Theme.of(context)
                                        .textTheme
                                        .labelSmall
                                        ?.copyWith(
                                          color: colorScheme.onSurfaceVariant,
                                        ),
                                  ),
                                  if (provider.hasCache) ...[
                                    const SizedBox(width: 8),
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 6,
                                        vertical: 2,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.green.withOpacity(0.1),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Icon(
                                            Icons.offline_bolt,
                                            color: Colors.green,
                                            size: 10,
                                          ),
                                          const SizedBox(width: 2),
                                          Text(
                                            'Cached',
                                            style: Theme.of(context)
                                                .textTheme
                                                .labelSmall
                                                ?.copyWith(
                                                  color: Colors.green,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ],
                              ),
                            ],
                          ],
                        ),
                      ),
                      if (provider.isLoadingFromCache)
                        SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              colorScheme.primary,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),

                // Main content
                Expanded(
                  child: _buildMainContent(context, provider, colorScheme),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildMainContent(
    BuildContext context,
    HistoryProvider provider,
    ColorScheme colorScheme,
  ) {
    if (provider.isLoading && provider.historyItems.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(
              'Loading history...',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      );
    }

    if (provider.errorMessage.isNotEmpty && provider.historyItems.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                _getErrorIcon(provider.errorMessage),
                color: provider.isRateLimited ? Colors.orange : Colors.red,
                size: 64,
              ),
              const SizedBox(height: 24),
              Text(
                provider.isRateLimited
                    ? 'Server is Busy'
                    : 'Unable to Load History',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  color: colorScheme.onSurface,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                _getErrorDescription(provider.errorMessage),
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: colorScheme.onSurfaceVariant,
                  height: 1.4,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 8),
              Text(
                provider.errorMessage,
                style: Theme.of(context).textTheme.labelSmall?.copyWith(
                  color: colorScheme.onSurfaceVariant,
                  fontStyle: FontStyle.italic,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ElevatedButton.icon(
                    onPressed: () => provider.refreshHistory(),
                    icon: const Icon(Icons.refresh),
                    label: const Text('Try Again'),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 12,
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  OutlinedButton.icon(
                    onPressed: () {
                      // Navigate to Voice Assistant tab
                      Navigator.of(context).pop();
                    },
                    icon: const Icon(Icons.mic),
                    label: const Text('Record New'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 12,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    }

    if (provider.historyItems.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.history, color: colorScheme.onSurfaceVariant, size: 48),
            const SizedBox(height: 16),
            Text(
              'No transcriptions yet',
              style: Theme.of(
                context,
              ).textTheme.headlineSmall?.copyWith(color: colorScheme.onSurface),
            ),
            const SizedBox(height: 8),
            Text(
              'Your transcription history will appear here',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // Error banner for partial errors (when we have items but failed to refresh)
        if (provider.errorMessage.isNotEmpty &&
            provider.historyItems.isNotEmpty)
          Container(
            width: double.infinity,
            margin: const EdgeInsets.fromLTRB(16, 16, 16, 0),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.orange.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.orange.withOpacity(0.3)),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.warning_amber_rounded,
                  color: Colors.orange,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Failed to refresh history',
                        style: Theme.of(context).textTheme.labelMedium
                            ?.copyWith(
                              color: Colors.orange,
                              fontWeight: FontWeight.w600,
                            ),
                      ),
                      Text(
                        'Tap to refresh',
                        style: Theme.of(
                          context,
                        ).textTheme.labelSmall?.copyWith(color: Colors.orange),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => provider.refreshHistory(),
                  icon: Icon(Icons.refresh, color: Colors.orange, size: 18),
                  constraints: const BoxConstraints(),
                  padding: EdgeInsets.zero,
                ),
              ],
            ),
          ),

        // History list
        Expanded(
          child: ListView.builder(
            controller: _scrollController,
            padding: const EdgeInsets.all(16),
            itemCount:
                provider.historyItems.length + (provider.isLoadingMore ? 1 : 0),
            itemBuilder: (context, index) {
              if (index == provider.historyItems.length) {
                return const Center(
                  child: Padding(
                    padding: EdgeInsets.all(16),
                    child: CircularProgressIndicator(),
                  ),
                );
              }

              final item = provider.historyItems[index];
              return _buildHistoryItem(item, colorScheme);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildHistoryItem(
    TranscriptionHistoryItem item,
    ColorScheme colorScheme,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerLow,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: colorScheme.outline, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(color: colorScheme.outline, width: 1),
              ),
            ),
            child: Row(
              children: [
                // Type indicator
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: item.type == TranscriptionType.recording
                        ? Colors.blue.withOpacity(0.2)
                        : Colors.green.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        item.type == TranscriptionType.recording
                            ? Icons.mic
                            : Icons.stream,
                        size: 12,
                        color: item.type == TranscriptionType.recording
                            ? Colors.blue
                            : Colors.green,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        item.typeDisplay,
                        style: Theme.of(context).textTheme.labelMedium
                            ?.copyWith(
                              color: item.type == TranscriptionType.recording
                                  ? Colors.blue
                                  : Colors.green,
                            ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 12),

                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _formatDate(item.createdAt),
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(color: colorScheme.onSurface),
                      ),
                      if (item.filename != null && item.filename!.isNotEmpty)
                        Text(
                          item.filename!,
                          style: Theme.of(context).textTheme.labelSmall
                              ?.copyWith(color: colorScheme.onSurfaceVariant),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                    ],
                  ),
                ),

                // Status indicator
                _buildStatusIndicator(context, item),
              ],
            ),
          ),

          // Content
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Duration and credits
                Row(
                  children: [
                    Icon(
                      Icons.access_time,
                      size: 16,
                      color: colorScheme.onSurfaceVariant,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      item.durationFormatted,
                      style: Theme.of(context).textTheme.labelSmall?.copyWith(
                        color: colorScheme.onSurfaceVariant,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Icon(
                      Icons.monetization_on,
                      size: 16,
                      color: colorScheme.onSurfaceVariant,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${item.creditsUsed} credits',
                      style: Theme.of(context).textTheme.labelSmall?.copyWith(
                        color: colorScheme.onSurfaceVariant,
                      ),
                    ),
                    if (item.confidence != null) ...[
                      const SizedBox(width: 16),
                      Icon(
                        Icons.psychology,
                        size: 16,
                        color: colorScheme.onSurfaceVariant,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${(item.confidence! * 100).toStringAsFixed(0)}%',
                        style: Theme.of(context).textTheme.labelSmall?.copyWith(
                          color: colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ],
                ),

                // Transcription text
                if (item.text != null && item.text!.isNotEmpty) ...[
                  const SizedBox(height: 12),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: colorScheme.surface,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: colorScheme.outline, width: 1),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Text(
                              'Transcription:',
                              style: Theme.of(context).textTheme.labelMedium
                                  ?.copyWith(
                                    color: colorScheme.onSurfaceVariant,
                                  ),
                            ),
                            const Spacer(),
                            IconButton(
                              onPressed: () {
                                // Validate we're copying the complete database text
                                final fullText = item.text!;
                                final fullWordCount = fullText
                                    .trim()
                                    .split(RegExp(r'\s+'))
                                    .length;
                                print(
                                  '📋 Copy button: Full text length=${fullText.length} chars, words=$fullWordCount',
                                );
                                print(
                                  '📋 Database word count: ${item.wordCount}',
                                );

                                _copyToClipboard(fullText);
                              },
                              icon: Icon(
                                Icons.copy,
                                size: 16,
                                color: colorScheme.onSurfaceVariant,
                              ),
                              tooltip:
                                  'Copy full transcription text (${item.wordCount ?? 0} words)',
                              constraints: const BoxConstraints(),
                              padding: EdgeInsets.zero,
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        _buildTranscriptionText(context, item, colorScheme),
                        if (item.wordCount != null) ...[
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Text(
                                '${item.wordCount} words',
                                style: Theme.of(context).textTheme.labelSmall
                                    ?.copyWith(
                                      color: colorScheme.onSurfaceVariant,
                                    ),
                              ),
                              if (item.text != null &&
                                  item.text!.length > 200) ...[
                                const SizedBox(width: 8),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 6,
                                    vertical: 2,
                                  ),
                                  decoration: BoxDecoration(
                                    color: colorScheme.primary.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: Text(
                                    'Full text: ${item.wordCount ?? 0} words',
                                    style: Theme.of(context)
                                        .textTheme
                                        .labelSmall
                                        ?.copyWith(
                                          color: colorScheme.primary,
                                          fontSize: 10,
                                        ),
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ],
                      ],
                    ),
                  ),
                ] else if (item.isCompleted) ...[
                  const SizedBox(height: 12),
                  Text(
                    'No transcription text available',
                    style: Theme.of(context).textTheme.labelSmall?.copyWith(
                      color: colorScheme.onSurfaceVariant,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ] else if (item.isProcessing) ...[
                  const SizedBox(height: 12),
                  Text(
                    'Transcription in progress...',
                    style: Theme.of(context).textTheme.labelSmall?.copyWith(
                      color: Colors.orange,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ] else if (item.isError) ...[
                  const SizedBox(height: 12),
                  Text(
                    'Transcription failed',
                    style: Theme.of(context).textTheme.labelSmall?.copyWith(
                      color: colorScheme.error,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusIndicator(
    BuildContext context,
    TranscriptionHistoryItem item,
  ) {
    Color color;
    IconData icon;

    if (item.isCompleted) {
      color = Colors.green;
      icon = Icons.check_circle;
    } else if (item.isError) {
      color = Colors.red;
      icon = Icons.error;
    } else if (item.isProcessing) {
      color = Colors.orange;
      icon = Icons.hourglass_empty;
    } else {
      color = Colors.grey;
      icon = Icons.help;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: color),
          const SizedBox(width: 4),
          Text(
            item.statusDisplay,
            style: Theme.of(
              context,
            ).textTheme.labelMedium?.copyWith(color: color),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return TimezoneUtils.formatForDisplay(date);
  }

  String _formatTime(DateTime date) {
    return TimezoneUtils.formatTime12Hour(date);
  }

  void _copyToClipboard(String text) async {
    try {
      // Validate the text we're about to copy
      final actualWordCount = text.trim().split(RegExp(r'\s+')).length;
      print(
        '📋 Copying text: ${text.length} characters, $actualWordCount words',
      );
      print(
        '📋 Text preview: "${text.substring(0, text.length > 50 ? 50 : text.length)}..."',
      );

      await ClipboardService.copyToClipboard(text);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Copied'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Failed to copy: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }

  IconData _getErrorIcon(String errorMessage) {
    final lowerMessage = errorMessage.toLowerCase();

    if (lowerMessage.contains('server is busy') ||
        lowerMessage.contains('rate limit')) {
      return Icons.hourglass_empty;
    } else if (lowerMessage.contains('timeout') ||
        lowerMessage.contains('connection')) {
      return Icons.wifi_off;
    } else if (lowerMessage.contains('authentication') ||
        lowerMessage.contains('unauthorized')) {
      return Icons.lock_outline;
    } else if (lowerMessage.contains('network') ||
        lowerMessage.contains('internet')) {
      return Icons.signal_wifi_off;
    } else if (lowerMessage.contains('server')) {
      return Icons.cloud_off;
    } else {
      return Icons.error_outline;
    }
  }

  String _getErrorDescription(String errorMessage) {
    final lowerMessage = errorMessage.toLowerCase();

    if (lowerMessage.contains('server is busy') ||
        lowerMessage.contains('rate limit')) {
      return 'The server is handling a lot of requests right now. Please wait a moment and try again.';
    } else if (lowerMessage.contains('timeout')) {
      return 'The request took too long to complete. This might be due to a slow internet connection.';
    } else if (lowerMessage.contains('connection') ||
        lowerMessage.contains('network')) {
      return 'Unable to connect to the server. Please check your internet connection and try again.';
    } else if (lowerMessage.contains('authentication') ||
        lowerMessage.contains('unauthorized')) {
      return 'Your session has expired. Please log out and log back in to continue.';
    } else if (lowerMessage.contains('server error')) {
      return 'The server is experiencing technical difficulties. Please try again in a few moments.';
    } else if (lowerMessage.contains('internet')) {
      return 'No internet connection detected. Please check your network settings.';
    } else {
      return 'Something went wrong while loading your transcription history. Please try again.';
    }
  }

  Widget _buildTranscriptionText(
    BuildContext context,
    TranscriptionHistoryItem item,
    ColorScheme colorScheme,
  ) {
    if (item.text == null || item.text!.isEmpty) {
      return const SizedBox.shrink();
    }

    final fullText = item.text!;
    const maxLength = 200; // Maximum characters to show when collapsed
    final isLongText = fullText.length > maxLength;
    final isExpanded = _expandedItems.contains(item.transcriptionId);

    // Debug: Log the actual text being processed for display
    final fullWordCount = fullText.trim().split(RegExp(r'\s+')).length;
    print('🎨 Display processing for ${item.transcriptionId}:');
    print('🎨 Full text: ${fullText.length} chars, $fullWordCount words');
    print('🎨 Database word count: ${item.wordCount}');
    print('🎨 Is long text (>${maxLength} chars): $isLongText');
    print(
      '🎨 Text preview: "${fullText.substring(0, fullText.length > 50 ? 50 : fullText.length)}..."',
    );

    String displayText;
    if (isLongText && !isExpanded) {
      // Find a good break point near maxLength (prefer word boundaries)
      int cutPoint = maxLength;
      if (cutPoint < fullText.length) {
        // Try to cut at a word boundary
        final spaceIndex = fullText.lastIndexOf(' ', cutPoint);
        if (spaceIndex > maxLength - 50) {
          // Don't cut too early
          cutPoint = spaceIndex;
        }
      }
      displayText = fullText.substring(0, cutPoint) + '...';
      final displayWordCount = displayText
          .replaceAll('...', '')
          .trim()
          .split(RegExp(r'\s+'))
          .length;
      print(
        '🎨 Truncated for display: ${displayText.length} chars, $displayWordCount words',
      );
    } else {
      displayText = fullText;
      print(
        '🎨 Showing full text: ${displayText.length} chars, $fullWordCount words',
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          displayText,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: colorScheme.onSurface,
            height: 1.4,
          ),
        ),
        if (isLongText) ...[
          const SizedBox(height: 4),
          GestureDetector(
            onTap: () {
              setState(() {
                if (isExpanded) {
                  _expandedItems.remove(item.transcriptionId);
                } else {
                  _expandedItems.add(item.transcriptionId);
                }
              });
            },
            child: Text(
              isExpanded ? 'Show less' : 'Show more',
              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                color: colorScheme.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ],
    );
  }
}
