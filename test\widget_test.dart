// This is a basic Flutter widget test for MurMur AI.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';

import 'package:murmur_ai/main.dart';

void main() {
  testWidgets('MurMur AI app smoke test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const MyApp());

    // Verify that our app shows the MurMur AI title
    expect(find.text('MurMur AI'), findsOneWidget);

    // The app should start with auth wrapper or login screen
    // Since we can't test actual auth without mocking, we just verify the app loads
    await tester.pump();
  });
}
