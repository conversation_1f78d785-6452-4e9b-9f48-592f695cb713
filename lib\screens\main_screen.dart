// import 'package:flutter/material.dart';
// import 'package:provider/provider.dart';
// import '../providers/auth_provider.dart';
// import '../utils/timezone_utils.dart';
// import 'voice_assistant_screen.dart';
// import 'history_screen.dart';
// import 'settings_screen.dart';

// class MainScreen extends StatefulWidget {
//   const MainScreen({super.key});

//   @override
//   State<MainScreen> createState() => _MainScreenState();
// }

// class _MainScreenState extends State<MainScreen> {
//   String selectedPage = 'MurMur AI';
//   bool sidebarCollapsed = false;
//   double get sidebarWidth => sidebarCollapsed ? 60 : 280;

//   final List<NavItem> navItems = [
//     NavItem('MurMur AI', Icons.mic, const VoiceAssistantContent()),
//     NavItem('History', Icons.history, const HistoryScreen()),
//     NavItem('Settings', Icons.settings, const SettingsScreen()),
//   ];

//   @override
//   Widget build(BuildContext context) {
//     final theme = Theme.of(context);
//     final colorScheme = theme.colorScheme;

//     return Scaffold(
//       backgroundColor: colorScheme.surface,
//       body: Row(
//         children: [
//           // Left Sidebar
//           AnimatedContainer(
//             duration: const Duration(milliseconds: 200),
//             width: sidebarWidth,
//             decoration: BoxDecoration(
//               color: colorScheme.surfaceContainerLow,
//               border: Border(
//                 right: BorderSide(color: colorScheme.outline, width: 1),
//               ),
//             ),
//             child: LayoutBuilder(
//               builder: (context, constraints) {
//                 // Use the actual rendered width to determine layout
//                 final actualWidth = constraints.maxWidth;
//                 final isActuallyCollapsed = actualWidth < 200;

//                 return Column(
//                   children: [
//                     // Header with app info and collapse button
//                     Container(
//                       padding: const EdgeInsets.all(16),
//                       decoration: BoxDecoration(
//                         border: Border(
//                           bottom: BorderSide(
//                             color: colorScheme.outline,
//                             width: 1,
//                           ),
//                         ),
//                       ),
//                       child: isActuallyCollapsed
//                           ? Center(
//                               child: IconButton(
//                                 onPressed: () {
//                                   setState(() {
//                                     sidebarCollapsed = false;
//                                   });
//                                 },
//                                 icon: Icon(
//                                   Icons.menu_open,
//                                   color: colorScheme.onSurfaceVariant,
//                                   size: 20,
//                                 ),
//                                 tooltip: 'Expand sidebar',
//                               ),
//                             )
//                           : Row(
//                               children: [
//                                 Icon(
//                                   Icons.mic,
//                                   color: colorScheme.primary,
//                                   size: 24,
//                                 ),
//                                 const SizedBox(width: 12),
//                                 Expanded(
//                                   child: Text(
//                                     'MurMur AI',
//                                     style: theme.textTheme.headlineSmall
//                                         ?.copyWith(
//                                           color: colorScheme.onSurface,
//                                         ),
//                                   ),
//                                 ),
//                                 IconButton(
//                                   onPressed: () {
//                                     setState(() {
//                                       sidebarCollapsed = true;
//                                     });
//                                   },
//                                   icon: Icon(
//                                     Icons.menu,
//                                     color: colorScheme.onSurfaceVariant,
//                                     size: 20,
//                                   ),
//                                   tooltip: 'Collapse sidebar',
//                                 ),
//                               ],
//                             ),
//                     ),

//                     // User info section (only when expanded)
//                     if (!isActuallyCollapsed)
//                       Container(
//                         padding: const EdgeInsets.all(16),
//                         decoration: BoxDecoration(
//                           border: Border(
//                             bottom: BorderSide(
//                               color: colorScheme.outline,
//                               width: 1,
//                             ),
//                           ),
//                         ),
//                         child: Consumer<AuthProvider>(
//                           builder: (context, authProvider, child) {
//                             final user = authProvider.user;
//                             return Column(
//                               crossAxisAlignment: CrossAxisAlignment.start,
//                               children: [
//                                 Row(
//                                   children: [
//                                     CircleAvatar(
//                                       radius: 20,
//                                       backgroundColor: Colors.blue,
//                                       child: Text(
//                                         user?.fullName != null &&
//                                                 user!.fullName.isNotEmpty
//                                             ? user.fullName
//                                                   .split(' ')
//                                                   .map(
//                                                     (e) => e.isNotEmpty
//                                                         ? e[0]
//                                                         : '',
//                                                   )
//                                                   .where((e) => e.isNotEmpty)
//                                                   .take(2)
//                                                   .join()
//                                             : 'U',
//                                         style: TextStyle(
//                                           color: colorScheme.onPrimary,
//                                           fontWeight: FontWeight.w600,
//                                         ),
//                                       ),
//                                     ),
//                                     const SizedBox(width: 12),
//                                     Expanded(
//                                       child: Column(
//                                         crossAxisAlignment:
//                                             CrossAxisAlignment.start,
//                                         children: [
//                                           Text(
//                                             user?.fullName ?? 'User',
//                                             style: theme.textTheme.titleMedium
//                                                 ?.copyWith(
//                                                   color: colorScheme.onSurface,
//                                                 ),
//                                           ),
//                                           Text(
//                                             user?.email ?? '',
//                                             style: theme.textTheme.labelSmall
//                                                 ?.copyWith(
//                                                   color: colorScheme
//                                                       .onSurfaceVariant,
//                                                 ),
//                                             overflow: TextOverflow.ellipsis,
//                                           ),
//                                         ],
//                                       ),
//                                     ),
//                                   ],
//                                 ),
//                                 const SizedBox(height: 12),
//                                 Container(
//                                   padding: const EdgeInsets.symmetric(
//                                     horizontal: 8,
//                                     vertical: 4,
//                                   ),
//                                   decoration: BoxDecoration(
//                                     color: colorScheme.primary.withOpacity(0.2),
//                                     borderRadius: BorderRadius.circular(4),
//                                   ),
//                                   child: Row(
//                                     mainAxisSize: MainAxisSize.min,
//                                     children: [
//                                       Icon(
//                                         Icons.stars,
//                                         color: colorScheme.primary,
//                                         size: 14,
//                                       ),
//                                       const SizedBox(width: 4),
//                                       Text(
//                                         '${user?.creditsRemaining ?? 0} credits',
//                                         style: theme.textTheme.labelMedium
//                                             ?.copyWith(
//                                               color: colorScheme.primary,
//                                             ),
//                                       ),
//                                     ],
//                                   ),
//                                 ),
//                                 const SizedBox(height: 4),
//                                 Text(
//                                   '${user?.plan.toUpperCase() ?? 'FREE'} PLAN',
//                                   style: theme.textTheme.labelSmall?.copyWith(
//                                     color: colorScheme.onSurfaceVariant,
//                                     fontWeight: FontWeight.w600,
//                                     letterSpacing: 0.5,
//                                   ),
//                                 ),
//                               ],
//                             );
//                           },
//                         ),
//                       ),

//                     // Navigation items
//                     Expanded(
//                       child: ListView(
//                         padding: const EdgeInsets.symmetric(vertical: 8),
//                         children: navItems
//                             .map(
//                               (item) =>
//                                   _buildNavItem(item, isActuallyCollapsed),
//                             )
//                             .toList(),
//                       ),
//                     ),

//                     // Version display (only when expanded, above the line)
//                     if (!isActuallyCollapsed) ...[
//                       Container(
//                         padding: const EdgeInsets.symmetric(vertical: 12),
//                         child: Center(
//                           child: Text(
//                             'v1.0.0',
//                             style: theme.textTheme.labelSmall?.copyWith(
//                               color: colorScheme.onSurfaceVariant.withOpacity(
//                                 0.6,
//                               ),
//                             ),
//                           ),
//                         ),
//                       ),
//                     ],

//                     // Logout section at bottom
//                     Container(
//                       padding: const EdgeInsets.all(8),
//                       decoration: BoxDecoration(
//                         border: Border(
//                           top: BorderSide(color: colorScheme.outline, width: 1),
//                         ),
//                       ),
//                       child: _buildLogoutButton(isActuallyCollapsed),
//                     ),
//                   ],
//                 );
//               },
//             ),
//           ),

//           // Main content area
//           Expanded(child: _getSelectedPageWidget()),
//         ],
//       ),
//     );
//   }

//   Widget _buildNavItem(NavItem item, bool isCollapsed) {
//     final isSelected = selectedPage == item.name;
//     final colorScheme = Theme.of(context).colorScheme;

//     if (isCollapsed) {
//       // Use a simple container with centered icon for collapsed state
//       return Container(
//         margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
//         decoration: BoxDecoration(
//           color: isSelected
//               ? colorScheme.secondaryContainer
//               : Colors.transparent,
//           borderRadius: BorderRadius.circular(8),
//         ),
//         child: Material(
//           color: Colors.transparent,
//           child: Tooltip(
//             message: item.name,
//             child: InkWell(
//               borderRadius: BorderRadius.circular(8),
//               onTap: () {
//                 setState(() {
//                   selectedPage = item.name;
//                 });
//               },
//               child: Container(
//                 height: 44,
//                 child: Center(
//                   child: Icon(
//                     item.icon,
//                     color: isSelected
//                         ? colorScheme.onSecondaryContainer
//                         : colorScheme.onSurfaceVariant,
//                     size: 20,
//                   ),
//                 ),
//               ),
//             ),
//           ),
//         ),
//       );
//     }

//     // Use ListTile for expanded state
//     return Container(
//       margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
//       decoration: BoxDecoration(
//         color: isSelected ? colorScheme.secondaryContainer : Colors.transparent,
//         borderRadius: BorderRadius.circular(8),
//       ),
//       child: ListTile(
//         dense: true,
//         contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
//         leading: Icon(
//           item.icon,
//           color: isSelected
//               ? colorScheme.onSecondaryContainer
//               : colorScheme.onSurfaceVariant,
//           size: 20,
//         ),
//         title: Text(
//           item.name,
//           style: Theme.of(context).textTheme.titleMedium?.copyWith(
//             color: isSelected
//                 ? colorScheme.onSecondaryContainer
//                 : colorScheme.onSurfaceVariant,
//             fontWeight: isSelected ? FontWeight.w500 : FontWeight.w400,
//           ),
//         ),
//         onTap: () {
//           setState(() {
//             selectedPage = item.name;
//           });
//         },
//       ),
//     );
//   }

//   Widget _buildLogoutButton(bool isCollapsed) {
//     final colorScheme = Theme.of(context).colorScheme;
//     return Consumer<AuthProvider>(
//       builder: (context, authProvider, child) {
//         if (isCollapsed) {
//           // Use simple container for collapsed state
//           return Container(
//             margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
//             child: Material(
//               color: Colors.transparent,
//               child: Tooltip(
//                 message: 'Logout',
//                 child: InkWell(
//                   borderRadius: BorderRadius.circular(8),
//                   onTap: () async {
//                     await authProvider.logout();
//                   },
//                   child: Container(
//                     height: 44,
//                     child: Center(
//                       child: Icon(
//                         Icons.logout,
//                         color: colorScheme.error,
//                         size: 20,
//                       ),
//                     ),
//                   ),
//                 ),
//               ),
//             ),
//           );
//         }

//         // Use ListTile for expanded state
//         return ListTile(
//           dense: true,
//           contentPadding: const EdgeInsets.symmetric(
//             horizontal: 16,
//             vertical: 4,
//           ),
//           leading: Icon(Icons.logout, color: colorScheme.error, size: 20),
//           title: Text(
//             'Logout',
//             style: Theme.of(
//               context,
//             ).textTheme.titleMedium?.copyWith(color: colorScheme.error),
//           ),
//           onTap: () async {
//             await authProvider.logout();
//           },
//         );
//       },
//     );
//   }

//   Widget _getSelectedPageWidget() {
//     final selectedItem = navItems.firstWhere(
//       (item) => item.name == selectedPage,
//       orElse: () => navItems[0],
//     );
//     return selectedItem.widget;
//   }
// }

// class NavItem {
//   final String name;
//   final IconData icon;
//   final Widget widget;

//   NavItem(this.name, this.icon, this.widget);
// }

// // Wrapper for the original voice assistant content
// class VoiceAssistantTab extends StatelessWidget {
//   const VoiceAssistantTab({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return const VoiceAssistantContent();
//   }
// }

import 'package:bitsdojo_window/bitsdojo_window.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../utils/timezone_utils.dart';
import '../widgets/window_buttons.dart';
import 'voice_assistant_screen.dart';
import 'history_screen.dart';
import 'settings_screen.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  String selectedPage = 'MurMur AI';
  bool sidebarCollapsed = false;
  double get sidebarWidth => sidebarCollapsed ? 60 : 280;

  final List<NavItem> navItems = [
    NavItem('MurMur AI', Icons.mic, const VoiceAssistantContent()),
    NavItem('History', Icons.history, const HistoryScreen()),
    NavItem('Settings', Icons.settings, const SettingsScreen()),
  ];

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      body: Column(
        children: [
          WindowTitleBarBox(
            child: Row(
              children: [
                Expanded(child: MoveWindow()),
                const WindowButtons(),
              ],
            ),
          ),
          Expanded(
            child: Row(
              children: [
                // Left Sidebar
                AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  width: sidebarWidth,
                  decoration: BoxDecoration(
                    color: colorScheme.surfaceContainerLow,
                    border: Border(
                      right: BorderSide(color: colorScheme.outline, width: 1),
                    ),
                  ),
                  child: LayoutBuilder(
                    builder: (context, constraints) {
                      // Use the actual rendered width to determine layout
                      final actualWidth = constraints.maxWidth;
                      final isActuallyCollapsed = actualWidth < 200;

                      return Column(
                        children: [
                          // Header with app info and collapse button
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              border: Border(
                                bottom: BorderSide(
                                  color: colorScheme.outline,
                                  width: 1,
                                ),
                              ),
                            ),
                            child: isActuallyCollapsed
                                ? Center(
                                    child: IconButton(
                                      onPressed: () {
                                        setState(() {
                                          sidebarCollapsed = false;
                                        });
                                      },
                                      icon: Icon(
                                        Icons.menu_open,
                                        color: colorScheme.onSurfaceVariant,
                                        size: 20,
                                      ),
                                      tooltip: 'Expand sidebar',
                                    ),
                                  )
                                : Row(
                                    children: [
                                      Icon(
                                        Icons.mic,
                                        color: colorScheme.primary,
                                        size: 24,
                                      ),
                                      const SizedBox(width: 12),
                                      Expanded(
                                        child: Text(
                                          'MurMur AI',
                                          style: theme.textTheme.headlineSmall
                                              ?.copyWith(
                                                color: colorScheme.onSurface,
                                              ),
                                        ),
                                      ),
                                      IconButton(
                                        onPressed: () {
                                          setState(() {
                                            sidebarCollapsed = true;
                                          });
                                        },
                                        icon: Icon(
                                          Icons.menu,
                                          color: colorScheme.onSurfaceVariant,
                                          size: 20,
                                        ),
                                        tooltip: 'Collapse sidebar',
                                      ),
                                    ],
                                  ),
                          ),

                          // User info section (only when expanded)
                          if (!isActuallyCollapsed)
                            Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                border: Border(
                                  bottom: BorderSide(
                                    color: colorScheme.outline,
                                    width: 1,
                                  ),
                                ),
                              ),
                              child: Consumer<AuthProvider>(
                                builder: (context, authProvider, child) {
                                  final user = authProvider.user;
                                  return Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          CircleAvatar(
                                            radius: 20,
                                            backgroundColor: Colors.blue,
                                            child: Text(
                                              user?.fullName != null &&
                                                      user!.fullName.isNotEmpty
                                                  ? user.fullName
                                                        .split(' ')
                                                        .map(
                                                          (e) => e.isNotEmpty
                                                              ? e[0]
                                                              : '',
                                                        )
                                                        .where(
                                                          (e) => e.isNotEmpty,
                                                        )
                                                        .take(2)
                                                        .join()
                                                  : 'U',
                                              style: TextStyle(
                                                color: colorScheme.onPrimary,
                                                fontWeight: FontWeight.w600,
                                              ),
                                            ),
                                          ),
                                          const SizedBox(width: 12),
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  user?.fullName ?? 'User',
                                                  style: theme
                                                      .textTheme
                                                      .titleMedium
                                                      ?.copyWith(
                                                        color: colorScheme
                                                            .onSurface,
                                                      ),
                                                ),
                                                Text(
                                                  user?.email ?? '',
                                                  style: theme
                                                      .textTheme
                                                      .labelSmall
                                                      ?.copyWith(
                                                        color: colorScheme
                                                            .onSurfaceVariant,
                                                      ),
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 12),
                                      Container(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 8,
                                          vertical: 4,
                                        ),
                                        decoration: BoxDecoration(
                                          color: colorScheme.primary
                                              .withOpacity(0.2),
                                          borderRadius: BorderRadius.circular(
                                            4,
                                          ),
                                        ),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Icon(
                                              Icons.stars,
                                              color: colorScheme.primary,
                                              size: 14,
                                            ),
                                            const SizedBox(width: 4),
                                            Text(
                                              '${user?.creditsRemaining ?? 0} credits',
                                              style: theme.textTheme.labelMedium
                                                  ?.copyWith(
                                                    color: colorScheme.primary,
                                                  ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        '${user?.plan.toUpperCase() ?? 'FREE'} PLAN',
                                        style: theme.textTheme.labelSmall
                                            ?.copyWith(
                                              color:
                                                  colorScheme.onSurfaceVariant,
                                              fontWeight: FontWeight.w600,
                                              letterSpacing: 0.5,
                                            ),
                                      ),
                                    ],
                                  );
                                },
                              ),
                            ),

                          // Navigation items
                          Expanded(
                            child: ListView(
                              padding: const EdgeInsets.symmetric(vertical: 8),
                              children: navItems
                                  .map(
                                    (item) => _buildNavItem(
                                      item,
                                      isActuallyCollapsed,
                                    ),
                                  )
                                  .toList(),
                            ),
                          ),

                          // Version display (only when expanded, above the line)
                          if (!isActuallyCollapsed) ...[
                            Container(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              child: Center(
                                child: Text(
                                  'v1.0.0',
                                  style: theme.textTheme.labelSmall?.copyWith(
                                    color: colorScheme.onSurfaceVariant
                                        .withOpacity(0.6),
                                  ),
                                ),
                              ),
                            ),
                          ],

                          // Logout Button
                          _buildLogoutButton(isActuallyCollapsed),
                        ],
                      );
                    },
                  ),
                ),

                // Main content area
                Expanded(child: _getSelectedPageWidget()),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavItem(NavItem item, bool isCollapsed) {
    final isSelected = selectedPage == item.title;
    final theme = Theme.of(context);
    final padding = isCollapsed
        ? const EdgeInsets.all(12)
        : const EdgeInsets.symmetric(horizontal: 16, vertical: 12);

    return Tooltip(
      message: isCollapsed ? item.title : '',
      child: InkWell(
        onTap: () {
          setState(() {
            selectedPage = item.title;
          });
        },
        child: Container(
          padding: padding,
          decoration: BoxDecoration(
            color: isSelected ? theme.colorScheme.primaryContainer : null,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisAlignment: isCollapsed
                ? MainAxisAlignment.center
                : MainAxisAlignment.start,
            children: [
              Icon(
                item.icon,
                size: 20,
                color: isSelected
                    ? theme.colorScheme.primary
                    : theme.colorScheme.onSurfaceVariant,
              ),
              if (!isCollapsed) ...[
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    item.title,
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: isSelected
                          ? theme.colorScheme.primary
                          : theme.colorScheme.onSurfaceVariant,
                      fontWeight: isSelected
                          ? FontWeight.w600
                          : FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLogoutButton(bool isCollapsed) {
    final theme = Theme.of(context);

    return Container(
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(color: theme.colorScheme.outline, width: 1),
        ),
      ),
      child: Tooltip(
        message: isCollapsed ? 'Logout' : '',
        child: InkWell(
          onTap: () {
            Provider.of<AuthProvider>(context, listen: false).logout();
          },
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: isCollapsed
                  ? MainAxisAlignment.center
                  : MainAxisAlignment.start,
              children: [
                Icon(Icons.logout, size: 20, color: theme.colorScheme.error),
                if (!isCollapsed) ...[
                  const SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      'Logout',
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: theme.colorScheme.error,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _getSelectedPageWidget() {
    final selectedItem = navItems.firstWhere(
      (item) => item.title == selectedPage,
      orElse: () => navItems[0],
    );
    return selectedItem.page;
  }
}

class NavItem {
  final String title;
  final IconData icon;
  final Widget page;

  NavItem(this.title, this.icon, this.page);
}
