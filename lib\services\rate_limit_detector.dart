import 'dart:async';

class RateLimitDetector {
  static final RateLimitDetector _instance = RateLimitDetector._internal();
  factory RateLimitDetector() => _instance;
  RateLimitDetector._internal();

  static RateLimitDetector get instance => _instance;

  DateTime? _lastRateLimitHit;
  int _rateLimitCount = 0;
  Timer? _resetTimer;
  String? _lastRateLimitType;
  String? _lastRateLimitDetails;
  String? _userPlan;

  bool get isCurrentlyRateLimited =>
      _lastRateLimitHit != null &&
      DateTime.now().difference(_lastRateLimitHit!).inMinutes < 10;

  int get rateLimitHitCount => _rateLimitCount;

  /// Call this when a 429 error is detected
  void recordRateLimit({
    String? rateLimitType,
    String? rateLimitDetails,
    String? userPlan,
  }) {
    _lastRateLimitHit = DateTime.now();
    _rateLimitCount++;
    _lastRateLimitType = rateLimitType;
    _lastRateLimitDetails = rateLimitDetails;
    _userPlan = userPlan;

    print('⚠️ Rate limit detected (count: $_rateLimitCount)');
    print(
      '📊 Type: $rateLimitType, Details: $rateLimitDetails, Plan: $userPlan',
    );

    // Reset counter after 1 hour
    _resetTimer?.cancel();
    _resetTimer = Timer(const Duration(hours: 1), () {
      _rateLimitCount = 0;
      print('🔄 Rate limit counter reset');
    });
  }

  /// Parse rate limit error from server response
  RateLimitInfo parseRateLimitError(String errorMessage, {String? userPlan}) {
    final lowerMessage = errorMessage.toLowerCase();

    // Extract rate limit information from error message
    if (lowerMessage.contains('api requests')) {
      if (lowerMessage.contains('per minute') ||
          lowerMessage.contains('/minute')) {
        return _getApiRequestsPerMinuteInfo(userPlan ?? 'free');
      } else if (lowerMessage.contains('per hour') ||
          lowerMessage.contains('/hour')) {
        return _getApiRequestsPerHourInfo(userPlan ?? 'free');
      } else if (lowerMessage.contains('per day') ||
          lowerMessage.contains('/day')) {
        return _getApiRequestsPerDayInfo(userPlan ?? 'free');
      }
    } else if (lowerMessage.contains('transcription')) {
      if (lowerMessage.contains('per hour') || lowerMessage.contains('/hour')) {
        return _getTranscriptionsPerHourInfo(userPlan ?? 'free');
      } else if (lowerMessage.contains('concurrent')) {
        return _getConcurrentTranscriptionsInfo(userPlan ?? 'free');
      }
    } else if (lowerMessage.contains('monthly minutes') ||
        lowerMessage.contains('quota')) {
      return _getMonthlyMinutesInfo(userPlan ?? 'free');
    } else if (lowerMessage.contains('file size')) {
      return _getFileSizeInfo(userPlan ?? 'free');
    } else if (lowerMessage.contains('authentication')) {
      return RateLimitInfo(
        title: 'Too Many Login Attempts',
        description:
            'You have exceeded the maximum number of login attempts. Please wait before trying again.',
        limit: '20 attempts per minute',
        suggestion: 'Wait a few minutes before attempting to log in again.',
        waitTime: '5 minutes',
        canUpgrade: false,
      );
    }

    // Default generic rate limit message
    return RateLimitInfo(
      title: 'Rate Limit Exceeded',
      description: 'You have exceeded your current plan\'s usage limits.',
      limit: 'Plan-specific limits',
      suggestion:
          'Please wait or consider upgrading your plan for higher limits.',
      waitTime: '10 minutes',
      canUpgrade: true,
    );
  }

  RateLimitInfo _getApiRequestsPerMinuteInfo(String plan) {
    final limits = _getPlanLimits(plan);
    return RateLimitInfo(
      title: 'API Request Limit Exceeded',
      description:
          'You have exceeded your ${limits['apiPerMinute']} API requests per minute limit.',
      limit: '${limits['apiPerMinute']} requests per minute',
      suggestion: plan == 'free'
          ? 'Upgrade to Basic Plan for 50 requests per minute or wait for the limit to reset.'
          : 'Please wait for the limit to reset or consider upgrading for higher limits.',
      waitTime: '1 minute',
      canUpgrade: plan != 'enterprise',
    );
  }

  RateLimitInfo _getApiRequestsPerHourInfo(String plan) {
    final limits = _getPlanLimits(plan);
    return RateLimitInfo(
      title: 'Hourly API Limit Exceeded',
      description:
          'You have exceeded your ${limits['apiPerHour']} API requests per hour limit.',
      limit: '${limits['apiPerHour']} requests per hour',
      suggestion: plan == 'free'
          ? 'Upgrade to Basic Plan for 1,000 requests per hour or wait for the limit to reset.'
          : 'Please wait for the limit to reset or consider upgrading for higher limits.',
      waitTime: '1 hour',
      canUpgrade: plan != 'enterprise',
    );
  }

  RateLimitInfo _getApiRequestsPerDayInfo(String plan) {
    final limits = _getPlanLimits(plan);
    return RateLimitInfo(
      title: 'Daily API Limit Exceeded',
      description:
          'You have exceeded your ${limits['apiPerDay']} API requests per day limit.',
      limit: '${limits['apiPerDay']} requests per day',
      suggestion: plan == 'free'
          ? 'Upgrade to Basic Plan for 10,000 requests per day or wait for the limit to reset.'
          : 'Please wait for the limit to reset or consider upgrading for higher limits.',
      waitTime: '24 hours',
      canUpgrade: plan != 'enterprise',
    );
  }

  RateLimitInfo _getTranscriptionsPerHourInfo(String plan) {
    final limits = _getPlanLimits(plan);
    return RateLimitInfo(
      title: 'Transcription Limit Exceeded',
      description:
          'You have exceeded your ${limits['transcriptionsPerHour']} transcriptions per hour limit.',
      limit: '${limits['transcriptionsPerHour']} transcriptions per hour',
      suggestion: plan == 'free'
          ? 'Upgrade to Basic Plan for 20 transcriptions per hour or wait for the limit to reset.'
          : 'Please wait for the limit to reset or consider upgrading for higher limits.',
      waitTime: '1 hour',
      canUpgrade: plan != 'enterprise',
    );
  }

  RateLimitInfo _getConcurrentTranscriptionsInfo(String plan) {
    final limits = _getPlanLimits(plan);
    return RateLimitInfo(
      title: 'Concurrent Transcription Limit',
      description:
          'You have ${limits['concurrentTranscriptions']} concurrent transcription(s) running. Please wait for one to complete.',
      limit: '${limits['concurrentTranscriptions']} concurrent transcriptions',
      suggestion: plan == 'free'
          ? 'Upgrade to Basic Plan for 3 concurrent transcriptions or wait for current ones to finish.'
          : 'Please wait for current transcriptions to complete or consider upgrading for higher limits.',
      waitTime: 'Until current transcriptions complete',
      canUpgrade: plan != 'enterprise',
    );
  }

  RateLimitInfo _getMonthlyMinutesInfo(String plan) {
    final limits = _getPlanLimits(plan);
    return RateLimitInfo(
      title: 'Monthly Minutes Exceeded',
      description:
          'You have used all ${limits['monthlyMinutes']} minutes included in your ${plan.toUpperCase()} plan.',
      limit: '${limits['monthlyMinutes']} minutes per month',
      suggestion: plan == 'free'
          ? 'Upgrade to Basic Plan for 500 minutes per month or wait for next month\'s reset.'
          : 'Consider upgrading your plan for more monthly minutes or wait for next month\'s reset.',
      waitTime: 'Until next month',
      canUpgrade: plan != 'enterprise',
    );
  }

  RateLimitInfo _getFileSizeInfo(String plan) {
    final limits = _getPlanLimits(plan);
    return RateLimitInfo(
      title: 'File Size Limit Exceeded',
      description:
          'Your file exceeds the ${limits['maxFileSize']} maximum file size limit.',
      limit: '${limits['maxFileSize']} maximum file size',
      suggestion: plan == 'free'
          ? 'Upgrade to Basic Plan for 100MB file size limit or use a smaller file.'
          : 'Please use a smaller file or consider upgrading for higher file size limits.',
      waitTime: 'N/A',
      canUpgrade: plan != 'enterprise',
    );
  }

  Map<String, dynamic> _getPlanLimits(String plan) {
    switch (plan.toLowerCase()) {
      case 'free':
        return {
          'apiPerMinute': 10,
          'apiPerHour': 100,
          'apiPerDay': 1000,
          'transcriptionsPerHour': 5,
          'concurrentTranscriptions': 1,
          'monthlyMinutes': 60,
          'maxFileSize': '25MB',
          'price': '₹0/month',
        };
      case 'basic':
        return {
          'apiPerMinute': 50,
          'apiPerHour': 1000,
          'apiPerDay': 10000,
          'transcriptionsPerHour': 20,
          'concurrentTranscriptions': 3,
          'monthlyMinutes': 500,
          'maxFileSize': '100MB',
          'price': '₹499/month',
        };
      case 'pro':
        return {
          'apiPerMinute': 100,
          'apiPerHour': 5000,
          'apiPerDay': 50000,
          'transcriptionsPerHour': 50,
          'concurrentTranscriptions': 5,
          'monthlyMinutes': 2000,
          'maxFileSize': '500MB',
          'price': '₹1,499/month',
        };
      case 'enterprise':
        return {
          'apiPerMinute': 500,
          'apiPerHour': 20000,
          'apiPerDay': 200000,
          'transcriptionsPerHour': 200,
          'concurrentTranscriptions': 20,
          'monthlyMinutes': 10000,
          'maxFileSize': '2GB',
          'price': '₹4,999/month',
        };
      default:
        return _getPlanLimits('free');
    }
  }

  /// Get user-friendly message for rate limiting
  String getRateLimitMessage() {
    if (!isCurrentlyRateLimited) {
      return 'Service is available';
    }

    final minutesSinceLimit = DateTime.now()
        .difference(_lastRateLimitHit!)
        .inMinutes;
    final waitTime = 10 - minutesSinceLimit;

    return 'Server is temporarily busy. '
        'This usually clears within $waitTime minutes.';
  }

  /// Get recommendation for user
  String getRecommendation() {
    if (_rateLimitCount > 3) {
      return 'Consider using the app less frequently for the next hour to avoid rate limits.';
    } else if (_rateLimitCount > 1) {
      return 'Try waiting a few minutes between actions to avoid hitting rate limits.';
    } else {
      return 'Server is temporarily busy. This is normal and will resolve shortly.';
    }
  }

  /// Check if we should skip an API call due to rate limiting
  bool shouldSkipApiCall() {
    return isCurrentlyRateLimited && _rateLimitCount > 2;
  }

  void dispose() {
    _resetTimer?.cancel();
  }
}

class RateLimitInfo {
  final String title;
  final String description;
  final String limit;
  final String suggestion;
  final String waitTime;
  final bool canUpgrade;

  RateLimitInfo({
    required this.title,
    required this.description,
    required this.limit,
    required this.suggestion,
    required this.waitTime,
    required this.canUpgrade,
  });
}
