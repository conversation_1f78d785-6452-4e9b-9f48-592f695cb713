import fal_client

# 0a451dbb-1f1f-4fa1-81ad-97c9ece765dc:160392e9edf5b2803e76df94a42f6983
# export FAL_KEY="0a451dbb-1f1f-4fa1-81ad-97c9ece765dc:160392e9edf5b2803e76df94a42f6983"

def on_queue_update(update):
    if isinstance(update, fal_client.InProgress):
        for log in update.logs:
           print(log["message"])

result = fal_client.subscribe(
    "fal-ai/kokoro/american-english",
    arguments={},
    with_logs=True,
    on_queue_update=on_queue_update,
)
print(result)