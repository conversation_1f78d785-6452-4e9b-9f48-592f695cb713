import '../utils/timezone_utils.dart';

class User {
  final String userId;
  final String email;
  final String fullName;
  final String plan;
  final int creditsRemaining;
  final bool emailVerified;
  final DateTime createdAt;
  final double? totalTranscriptionMinutes;
  final int? totalApiCalls;

  User({
    required this.userId,
    required this.email,
    required this.fullName,
    required this.plan,
    required this.creditsRemaining,
    required this.emailVerified,
    required this.createdAt,
    this.totalTranscriptionMinutes,
    this.totalApiCalls,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      userId: json['user_id'] ?? '',
      email: json['email'] ?? '',
      fullName: json['full_name'] ?? '',
      plan: json['plan'] ?? 'free',
      creditsRemaining: json['credits_remaining'] ?? 0,
      emailVerified: json['email_verified'] ?? false,
      createdAt: TimezoneUtils.parseToLocal(json['created_at']),
      totalTranscriptionMinutes: json['total_transcription_minutes']
          ?.toDouble(),
      totalApiCalls: json['total_api_calls'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'email': email,
      'full_name': fullName,
      'plan': plan,
      'credits_remaining': creditsRemaining,
      'email_verified': emailVerified,
      'created_at': TimezoneUtils.toUtcString(createdAt),
      'total_transcription_minutes': totalTranscriptionMinutes,
      'total_api_calls': totalApiCalls,
    };
  }
}

class AuthResponse {
  final String accessToken;
  final String refreshToken;
  final int expiresIn;
  final User user;

  AuthResponse({
    required this.accessToken,
    required this.refreshToken,
    required this.expiresIn,
    required this.user,
  });

  factory AuthResponse.fromJson(Map<String, dynamic> json) {
    return AuthResponse(
      accessToken: json['access_token'] ?? '',
      refreshToken: json['refresh_token'] ?? '',
      expiresIn: json['expires_in'] ?? 1800,
      user: User.fromJson(json['user'] ?? {}),
    );
  }
}

class RegisterRequest {
  final String email;
  final String password;
  final String firstName;
  final String lastName;
  final String? company;
  final String? phone;

  RegisterRequest({
    required this.email,
    required this.password,
    required this.firstName,
    required this.lastName,
    this.company,
    this.phone,
  });

  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'password': password,
      'first_name': firstName,
      'last_name': lastName,
      if (company != null) 'company': company,
      if (phone != null) 'phone': phone,
    };
  }
}

class LoginRequest {
  final String email;
  final String password;

  LoginRequest({required this.email, required this.password});

  Map<String, dynamic> toJson() {
    return {'email': email, 'password': password};
  }
}
