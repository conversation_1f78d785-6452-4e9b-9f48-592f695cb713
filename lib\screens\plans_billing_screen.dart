import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter/foundation.dart';
import 'dart:io';
import '../providers/auth_provider.dart';

class PlansBillingScreen extends StatefulWidget {
  const PlansBillingScreen({super.key});

  @override
  State<PlansBillingScreen> createState() => _PlansBillingScreenState();
}

class _PlansBillingScreenState extends State<PlansBillingScreen> {
  bool showAnnualPricing = false;

  // Subscription Plans Data
  final List<SubscriptionPlan> subscriptionPlans = [
    SubscriptionPlan(
      name: 'Free',
      displayName: 'Free Plan',
      monthlyPrice: 0,
      annualPrice: 0,
      includedMinutes: 60,
      maxFileSize: '25MB',
      concurrentTranscriptions: 1,
      apiCallsPerMinute: 10,
      dailyApiCalls: 100,
      realTimeTranscription: false,
      speakerIdentification: false,
      sentimentAnalysis: false,
      customVocabulary: false,
      prioritySupport: false,
      isPopular: false,
      isFeatured: false,
    ),
    SubscriptionPlan(
      name: 'Basic',
      displayName: 'Basic Plan',
      monthlyPrice: 899,
      annualPrice: 8954,
      includedMinutes: 500,
      maxFileSize: '100MB',
      concurrentTranscriptions: 3,
      apiCallsPerMinute: 50,
      dailyApiCalls: 5000,
      realTimeTranscription: true,
      speakerIdentification: true,
      sentimentAnalysis: false,
      customVocabulary: false,
      prioritySupport: false,
      isPopular: true,
      isFeatured: false,
    ),
    SubscriptionPlan(
      name: 'Pro',
      displayName: 'Pro Plan',
      monthlyPrice: 1499,
      annualPrice: 14930,
      includedMinutes: 2000,
      maxFileSize: '500MB',
      concurrentTranscriptions: 5,
      apiCallsPerMinute: 100,
      dailyApiCalls: 20000,
      realTimeTranscription: true,
      speakerIdentification: true,
      sentimentAnalysis: true,
      customVocabulary: true,
      prioritySupport: true,
      isPopular: false,
      isFeatured: true,
    ),
    SubscriptionPlan(
      name: 'Enterprise',
      displayName: 'Enterprise Plan',
      monthlyPrice: 4999,
      annualPrice: 49790,
      includedMinutes: 8000,
      maxFileSize: '2GB',
      concurrentTranscriptions: 20,
      apiCallsPerMinute: 500,
      dailyApiCalls: 100000,
      realTimeTranscription: true,
      speakerIdentification: true,
      sentimentAnalysis: true,
      customVocabulary: true,
      prioritySupport: true,
      isPopular: false,
      isFeatured: false,
    ),
  ];

  // Credit Packages Data
  final List<CreditPackage> creditPackages = [
    CreditPackage(
      credits: 60,
      price: 99,
      displayName: '1 Hour Pack',
      description: 'Perfect for occasional use',
      perMinuteRate: 1.65,
      discount: null,
    ),
    CreditPackage(
      credits: 300,
      price: 399,
      displayName: '5 Hour Pack',
      description: 'Great for regular users',
      perMinuteRate: 1.33,
      discount: 20,
    ),
    CreditPackage(
      credits: 600,
      price: 699,
      displayName: '10 Hour Pack',
      description: 'Best value for heavy usage',
      perMinuteRate: 1.16,
      discount: 30,
    ),
    CreditPackage(
      credits: 1800,
      price: 1799,
      displayName: '30 Hour Pack',
      description: 'Enterprise level usage',
      perMinuteRate: 1.00,
      discount: 40,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      appBar: AppBar(
        title: Text(
          'Plans & Billing',
          style: theme.textTheme.headlineMedium?.copyWith(
            color: colorScheme.onSurface,
          ),
        ),
        backgroundColor: colorScheme.surface,
        elevation: 0,
        surfaceTintColor: Colors.transparent,
        iconTheme: IconThemeData(color: colorScheme.onSurfaceVariant),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Subscription Plans Section
            _buildSubscriptionSection(theme, colorScheme),
            const SizedBox(height: 48),

            // Credit Packages Section
            _buildCreditPackagesSection(theme, colorScheme),
            const SizedBox(height: 48),

            // Credit System Explanation
            _buildCreditExplanationSection(theme, colorScheme),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildSubscriptionSection(ThemeData theme, ColorScheme colorScheme) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Subscription Plans',
            style: theme.textTheme.displaySmall?.copyWith(
              color: colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Choose the perfect plan for your transcription needs',
            style: theme.textTheme.bodyLarge?.copyWith(
              color: colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 24),

          // Annual/Monthly Toggle
          Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: colorScheme.surfaceContainerLow,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: colorScheme.outline),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildToggleButton(
                  'Monthly',
                  !showAnnualPricing,
                  () => setState(() => showAnnualPricing = false),
                  theme,
                  colorScheme,
                ),
                _buildToggleButton(
                  'Annual (17% off)',
                  showAnnualPricing,
                  () => setState(() => showAnnualPricing = true),
                  theme,
                  colorScheme,
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),

          // Plan Cards Grid
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
              childAspectRatio:
                  1.6, // Much more compact - eliminate blank space
            ),
            itemCount: subscriptionPlans.length,
            itemBuilder: (context, index) {
              return _buildPlanCard(
                subscriptionPlans[index],
                theme,
                colorScheme,
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildCreditPackagesSection(ThemeData theme, ColorScheme colorScheme) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Credit Packages',
            style: theme.textTheme.displaySmall?.copyWith(
              color: colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Pay-as-you-go flexibility with no monthly commitment',
            style: theme.textTheme.bodyLarge?.copyWith(
              color: colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 16),

          // Credit Packages Grid
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
              childAspectRatio:
                  2.5, // Extremely compact - eliminate blank space
            ),
            itemCount: creditPackages.length,
            itemBuilder: (context, index) {
              return _buildCreditPackageCard(
                creditPackages[index],
                theme,
                colorScheme,
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildCreditExplanationSection(
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 24),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: colorScheme.primaryContainer.withOpacity(0.3),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: colorScheme.primary.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info_outline, color: colorScheme.primary, size: 24),
              const SizedBox(width: 12),
              Text(
                'How Credits Work',
                style: theme.textTheme.headlineSmall?.copyWith(
                  color: colorScheme.onSurface,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildCreditExplanationItem(
            '1 Credit = 1 Minute',
            'Simple pricing with no hidden fees',
            Icons.timer,
            theme,
            colorScheme,
          ),
          const SizedBox(height: 12),
          _buildCreditExplanationItem(
            'Smart Rounding',
            '0.5+ minutes rounds up (e.g., 2.7 minutes = 3 credits)',
            Icons.calculate,
            theme,
            colorScheme,
          ),
          const SizedBox(height: 12),
          _buildCreditExplanationItem(
            'Never Expires',
            'Credits roll over month to month',
            Icons.all_inclusive,
            theme,
            colorScheme,
          ),
          const SizedBox(height: 12),
          _buildCreditExplanationItem(
            'Works with All Plans',
            'Credits supplement your included minutes',
            Icons.add_circle,
            theme,
            colorScheme,
          ),
        ],
      ),
    );
  }

  Widget _buildToggleButton(
    String text,
    bool isSelected,
    VoidCallback onTap,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? colorScheme.primary : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          text,
          style: theme.textTheme.titleSmall?.copyWith(
            color: isSelected
                ? colorScheme.onPrimary
                : colorScheme.onSurfaceVariant,
          ),
        ),
      ),
    );
  }

  Widget _buildPlanCard(
    SubscriptionPlan plan,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    final price = showAnnualPricing ? plan.annualPrice : plan.monthlyPrice;
    final discountPercent = showAnnualPricing && plan.monthlyPrice > 0
        ? (((plan.monthlyPrice * 12) - price) / (plan.monthlyPrice * 12) * 100)
              .round()
        : null;

    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final currentPlan = authProvider.user?.plan?.toLowerCase() ?? 'free';
        final isCurrentPlan = currentPlan == plan.name.toLowerCase();

        return Container(
          decoration: BoxDecoration(
            color: colorScheme.surfaceContainerLow,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: plan.isFeatured
                  ? colorScheme.primary
                  : colorScheme.outline,
              width: plan.isFeatured ? 2 : 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with badges
              Container(
                padding: const EdgeInsets.all(10),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Badges
                    Row(
                      children: [
                        if (plan.isFeatured)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: colorScheme.primary,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              'FEATURED',
                              style: theme.textTheme.labelSmall?.copyWith(
                                color: colorScheme.onPrimary,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        if (plan.isPopular)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.orange,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              'MOST POPULAR',
                              style: theme.textTheme.labelSmall?.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                      ],
                    ),
                    if (plan.isFeatured || plan.isPopular)
                      const SizedBox(height: 8),

                    // Plan name
                    Text(
                      plan.displayName,
                      style: theme.textTheme.headlineMedium?.copyWith(
                        color: colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 4),

                    // Price
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.baseline,
                      textBaseline: TextBaseline.alphabetic,
                      children: [
                        Text(
                          '₹${price.toStringAsFixed(0)}',
                          style: theme.textTheme.displayMedium?.copyWith(
                            color: colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          showAnnualPricing ? '/year' : '/month',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),

                    // Discount info
                    if (discountPercent != null)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          'Save ${discountPercent}% annually',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: Colors.green,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                  ],
                ),
              ),

              // Features
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 10),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildFeatureItem(
                        '${plan.includedMinutes} minutes/month',
                        true,
                        theme,
                        colorScheme,
                      ),
                      _buildFeatureItem(
                        'File size up to ${plan.maxFileSize}',
                        true,
                        theme,
                        colorScheme,
                      ),
                      _buildFeatureItem(
                        '${plan.concurrentTranscriptions} concurrent transcriptions',
                        true,
                        theme,
                        colorScheme,
                      ),
                      _buildFeatureItem(
                        '${plan.apiCallsPerMinute} API calls/minute',
                        true,
                        theme,
                        colorScheme,
                      ),
                      _buildFeatureItem(
                        '${plan.dailyApiCalls} daily API calls',
                        true,
                        theme,
                        colorScheme,
                      ),
                      _buildFeatureItem(
                        'Real-time transcription',
                        plan.realTimeTranscription,
                        theme,
                        colorScheme,
                      ),
                      _buildFeatureItem(
                        'Priority support',
                        plan.prioritySupport,
                        theme,
                        colorScheme,
                      ),
                    ],
                  ),
                ),
              ),

              // Action button
              Padding(
                padding: const EdgeInsets.all(10),
                child: SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: isCurrentPlan
                        ? null
                        : () => _handlePlanSelection(plan),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: isCurrentPlan
                          ? colorScheme.surfaceContainerHigh
                          : plan.isFeatured
                          ? colorScheme.primary
                          : colorScheme.primaryContainer,
                      foregroundColor: isCurrentPlan
                          ? colorScheme.onSurfaceVariant
                          : plan.isFeatured
                          ? colorScheme.onPrimary
                          : colorScheme.onPrimaryContainer,
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      isCurrentPlan ? 'Current Plan' : 'Upgrade Now',
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildCreditPackageCard(
    CreditPackage package,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerLow,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: colorScheme.outline),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(10),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Discount badge
                if (package.discount != null)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.orange,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${package.discount}% OFF',
                      style: theme.textTheme.labelSmall?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                if (package.discount != null) const SizedBox(height: 8),

                // Package name
                Text(
                  package.displayName,
                  style: theme.textTheme.headlineMedium?.copyWith(
                    color: colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  package.description,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                  ),
                ),
                const SizedBox(height: 8),

                // Price
                Row(
                  crossAxisAlignment: CrossAxisAlignment.baseline,
                  textBaseline: TextBaseline.alphabetic,
                  children: [
                    Text(
                      '₹${package.price}',
                      style: theme.textTheme.displayMedium?.copyWith(
                        color: colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '${package.credits} credits',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 2),
                Text(
                  '₹${package.perMinuteRate.toStringAsFixed(2)}/minute',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),

          // Action button
          Padding(
            padding: const EdgeInsets.all(10),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => _handleCreditPurchase(package),
                style: ElevatedButton.styleFrom(
                  backgroundColor: colorScheme.primary,
                  foregroundColor: colorScheme.onPrimary,
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  'Buy Credits',
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureItem(
    String text,
    bool included,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 1),
      child: Row(
        children: [
          Icon(
            included ? Icons.check_circle : Icons.remove_circle,
            color: included ? Colors.green : colorScheme.onSurfaceVariant,
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: theme.textTheme.bodySmall?.copyWith(
                color: included
                    ? colorScheme.onSurface
                    : colorScheme.onSurfaceVariant,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCreditExplanationItem(
    String title,
    String description,
    IconData icon,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    return Row(
      children: [
        Icon(icon, color: colorScheme.primary, size: 20),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: theme.textTheme.titleSmall?.copyWith(
                  color: colorScheme.onSurface,
                ),
              ),
              Text(
                description,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _handlePlanSelection(SubscriptionPlan plan) async {
    await _openUrl('http://localhost:3000');
  }

  void _handleCreditPurchase(CreditPackage package) async {
    await _openUrl('http://localhost:3000');
  }

  Future<void> _openUrl(String url) async {
    try {
      if (Platform.isWindows) {
        await Process.run('cmd', ['/c', 'start', url]);
      } else if (Platform.isMacOS) {
        await Process.run('open', [url]);
      } else if (Platform.isLinux) {
        await Process.run('xdg-open', [url]);
      } else {
        throw 'Platform not supported';
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Could not open browser: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

// Data Models
class SubscriptionPlan {
  final String name;
  final String displayName;
  final int monthlyPrice;
  final int annualPrice;
  final int includedMinutes;
  final String maxFileSize;
  final int concurrentTranscriptions;
  final int apiCallsPerMinute;
  final int dailyApiCalls;
  final bool realTimeTranscription;
  final bool speakerIdentification;
  final bool sentimentAnalysis;
  final bool customVocabulary;
  final bool prioritySupport;
  final bool isPopular;
  final bool isFeatured;

  SubscriptionPlan({
    required this.name,
    required this.displayName,
    required this.monthlyPrice,
    required this.annualPrice,
    required this.includedMinutes,
    required this.maxFileSize,
    required this.concurrentTranscriptions,
    required this.apiCallsPerMinute,
    required this.dailyApiCalls,
    required this.realTimeTranscription,
    required this.speakerIdentification,
    required this.sentimentAnalysis,
    required this.customVocabulary,
    required this.prioritySupport,
    required this.isPopular,
    required this.isFeatured,
  });
}

class CreditPackage {
  final int credits;
  final int price;
  final String displayName;
  final String description;
  final double perMinuteRate;
  final int? discount;

  CreditPackage({
    required this.credits,
    required this.price,
    required this.displayName,
    required this.description,
    required this.perMinuteRate,
    this.discount,
  });
}
