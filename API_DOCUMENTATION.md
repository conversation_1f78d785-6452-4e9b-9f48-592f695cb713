# 🎧 FastAPI AssemblyAI Middleware - API Documentation

## Overview

This API provides a comprehensive transcription service using AssemblyAI with subscription-based access, rate limiting, and user management. Perfect for integrating into Flutter applications that need audio transcription capabilities.

**Base URL:** `https://your-domain.com` or `http://localhost:8000` (development)

## 🔐 Authentication

### Authentication Methods

The API supports two authentication methods:

#### 1. JWT Bearer Token

- Obtain tokens via login
- Include in header: `Authorization: Bearer <access_token>`
- Tokens expire in 30 minutes
- Use refresh tokens to get new access tokens

#### 2. API Keys

- Generate via authenticated endpoint
- Include in header: `X-API-Key: <your_api_key>`
- No expiration but can be revoked

## 📊 Subscription Plans & Rate Limits

| Plan           | Price/Month | Included Minutes | Requests/Min | Requests/Day | Concurrent Transcriptions |
| -------------- | ----------- | ---------------- | ------------ | ------------ | ------------------------- |
| **Free**       | ₹0          | 60               | 10           | 1,000        | 1                         |
| **Basic**      | ₹499        | 500              | 50           | 5,000        | 3                         |
| **Pro**        | ₹1,499      | 2,000            | 100          | 20,000       | 5                         |
| **Enterprise** | ₹4,999      | 10,000           | 500          | 100,000      | 20                        |

### Rate Limit Headers

All responses include rate limit information:

- `X-RateLimit-Limit`: Your rate limit per minute
- `X-RateLimit-Remaining`: Remaining requests in current window
- `X-RateLimit-Reset`: Unix timestamp when the limit resets

## 🚀 API Endpoints

### Authentication Endpoints

#### Register User

**POST** `/register`

Create a new user account.

**Request Body:**

```json
{
  "email": "<EMAIL>",
  "password": "securepassword123",
  "first_name": "John",
  "last_name": "Doe",
  "company": "Tech Corp",
  "phone": "+**********"
}
```

**Response (201):**

```json
{
  "user_id": "abc123def456",
  "email": "<EMAIL>",
  "full_name": "John Doe",
  "plan": "free",
  "credits_remaining": 60,
  "email_verified": false,
  "created_at": "2024-01-15T10:30:00Z"
}
```

#### Login

**POST** `/login`

Authenticate user and receive JWT tokens.

**Request Body:**

```json
{
  "email": "<EMAIL>",
  "password": "securepassword123"
}
```

**Response (200):**

```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIs...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIs...",
  "expires_in": 1800,
  "user": {
    "user_id": "abc123def456",
    "email": "<EMAIL>",
    "full_name": "John Doe",
    "plan": "free",
    "credits_remaining": 60,
    "email_verified": false
  }
}
```

#### Refresh Token

**POST** `/refresh`

Get new access token using refresh token.

**Request Body:**

```json
{
  "refresh_token": "eyJhbGciOiJIUzI1NiIs..."
}
```

**Response (200):**

```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIs...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIs...",
  "expires_in": 1800
}
```

#### Logout

**POST** `/logout`

Invalidate current token.

**Headers:** `Authorization: Bearer <token>`

**Response (200):**

```json
{
  "message": "Successfully logged out"
}
```

#### Get Current User

**GET** `/me`

Get current user information.

**Headers:** `Authorization: Bearer <token>` or `X-API-Key: <api_key>`

**Response (200):**

```json
{
  "user_id": "abc123def456",
  "email": "<EMAIL>",
  "full_name": "John Doe",
  "plan": "pro",
  "credits_remaining": 1500,
  "total_transcription_minutes": 120.5,
  "total_api_calls": 45,
  "email_verified": true,
  "created_at": "2024-01-15T10:30:00Z"
}
```

#### Generate API Key

**POST** `/api-key`

Generate a new API key for programmatic access.

**Headers:** `Authorization: Bearer <token>`

**Response (200):**

```json
{
  "api_key": "ak_live_**********abcdef",
  "created_at": "2024-01-15T10:30:00Z",
  "message": "API key generated successfully"
}
```

#### Revoke API Key

**DELETE** `/api-key`

Revoke current API key.

**Headers:** `Authorization: Bearer <token>`

**Response (200):**

```json
{
  "message": "API key revoked successfully"
}
```

### Transcription Endpoints

#### Submit Audio for Transcription

**POST** `/transcribe`

Submit audio file or URL for transcription.

**Headers:** `Authorization: Bearer <token>` or `X-API-Key: <api_key>`

**Request Body (File Upload):**

```json
{
  "audio_file": "<multipart/form-data>",
  "options": {
    "speaker_labels": true,
    "auto_punctuation": true,
    "sentiment_analysis": false,
    "auto_highlights": false,
    "webhook_url": "https://your-app.com/webhook"
  }
}
```

**Request Body (URL):**

```json
{
  "audio_url": "https://example.com/audio.mp3",
  "options": {
    "speaker_labels": true,
    "auto_punctuation": true,
    "sentiment_analysis": false
  }
}
```

**Response (202):**

```json
{
  "transcription_id": "trans_123456789",
  "status": "queued",
  "estimated_completion": "2024-01-15T10:35:00Z",
  "credits_used": 5,
  "credits_remaining": 55
}
```

#### Get Transcription Status

**GET** `/transcribe/{transcription_id}`

Check transcription status and retrieve results.

**Headers:** `Authorization: Bearer <token>` or `X-API-Key: <api_key>`

**Response (200 - In Progress):**

```json
{
  "transcription_id": "trans_123456789",
  "status": "processing",
  "progress": 45,
  "estimated_completion": "2024-01-15T10:35:00Z"
}
```

**Response (200 - Completed):**

```json
{
  "transcription_id": "trans_123456789",
  "status": "completed",
  "text": "Hello, this is a sample transcription...",
  "confidence": 0.95,
  "audio_duration": 120,
  "word_count": 156,
  "credits_used": 2,
  "words": [
    {
      "text": "Hello",
      "start": 0.5,
      "end": 0.8,
      "confidence": 0.99
    }
  ],
  "speakers": [
    {
      "speaker": "A",
      "text": "Hello, this is speaker A",
      "start": 0.0,
      "end": 2.5
    }
  ],
  "completed_at": "2024-01-15T10:33:45Z"
}
```

#### Get Transcription History

**GET** `/history`

Get user's transcription history with pagination.

**Headers:** `Authorization: Bearer <token>` or `X-API-Key: <api_key>`

**Query Parameters:**

- `skip`: Number of records to skip (default: 0)
- `limit`: Number of records to return (default: 20, max: 100)
- `status`: Filter by status (queued, processing, completed, error)
- `start_date`: Filter from date (ISO format)
- `end_date`: Filter to date (ISO format)

**Response (200):**

```json
{
  "transcriptions": [
    {
      "transcription_id": "trans_123456789",
      "status": "completed",
      "created_at": "2024-01-15T10:30:00Z",
      "completed_at": "2024-01-15T10:33:45Z",
      "audio_duration": 120,
      "credits_used": 2,
      "filename": "meeting_recording.mp3"
    }
  ],
  "total": 25,
  "skip": 0,
  "limit": 20
}
```

#### Real-time Streaming Transcription

**POST** `/streaming`

Start real-time transcription session.

**Headers:** `Authorization: Bearer <token>` or `X-API-Key: <api_key>`

**Request Body:**

```json
{
  "sample_rate": 16000,
  "encoding": "pcm_s16le",
  "interim_results": true
}
```

**Response (200):**

```json
{
  "session_id": "stream_123456789",
  "websocket_url": "wss://your-domain.com/streaming/stream_123456789",
  "session_timeout": 300
}
```

### Payment Endpoints

#### Get Available Plans

**GET** `/api/v1/payment/plans`

Get all available subscription plans.

**Response (200):**

```json
[
  {
    "plan_id": 1,
    "name": "free",
    "display_name": "Free Plan",
    "description": "Perfect for trying out our service",
    "price": 0,
    "annual_price": 0,
    "included_minutes": 60,
    "features": {
      "max_file_size_mb": 25,
      "max_concurrent_transcriptions": 1,
      "api_calls_per_minute": 10,
      "real_time_transcription": false,
      "speaker_identification": false,
      "sentiment_analysis": false,
      "priority_support": false
    }
  }
]
```

#### Get Current Plan

**GET** `/api/v1/payment/current-plan`

Get user's current subscription plan details.

**Headers:** `Authorization: Bearer <token>` or `X-API-Key: <api_key>`

**Response (200):**

```json
{
  "plan": "pro",
  "display_name": "Pro Plan",
  "credits_remaining": 1500,
  "is_active": true,
  "subscribed_at": "2024-01-01T00:00:00Z",
  "features": {
    "included_minutes": 2000,
    "per_minute_rate": 1.25,
    "max_file_size_mb": 500,
    "max_concurrent_transcriptions": 5,
    "api_calls_per_minute": 100,
    "real_time_transcription": true,
    "speaker_identification": true,
    "sentiment_analysis": true,
    "priority_support": true
  }
}
```

#### Get Credit Packages

**GET** `/api/v1/payment/credit-packages`

Get available credit packages for purchase.

**Response (200):**

```json
[
  {
    "credits": 60,
    "price": 99,
    "currency": "INR",
    "display_name": "1 Hour Pack",
    "description": "Perfect for occasional use",
    "per_minute_rate": 1.65
  },
  {
    "credits": 300,
    "price": 399,
    "currency": "INR",
    "display_name": "5 Hour Pack",
    "description": "Great for regular users",
    "per_minute_rate": 1.33,
    "discount": "20% off"
  }
]
```

#### Create Payment Order

**POST** `/api/v1/payment/create-order`

Create a payment order for subscription or credits.

**Headers:** `Authorization: Bearer <token>` or `X-API-Key: <api_key>`

**Request Body (Credits):**

```json
{
  "amount": 399,
  "currency": "INR",
  "purpose": "credits",
  "credits": 300
}
```

**Request Body (Subscription):**

```json
{
  "amount": 1499,
  "currency": "INR",
  "purpose": "subscription",
  "plan_id": 3
}
```

**Response (200):**

```json
{
  "order_id": "order_**********",
  "amount": 39900,
  "currency": "INR",
  "key_id": "rzp_test_**********",
  "order": {
    "id": "order_**********",
    "amount": 39900,
    "currency": "INR",
    "status": "created"
  }
}
```

#### Verify Payment

**POST** `/api/v1/payment/verify`

Verify payment after successful transaction.

**Headers:** `Authorization: Bearer <token>` or `X-API-Key: <api_key>`

**Request Body:**

```json
{
  "razorpay_order_id": "order_**********",
  "razorpay_payment_id": "pay_**********",
  "razorpay_signature": "signature_hash"
}
```

**Response (200):**

```json
{
  "status": "success",
  "message": "Credits added successfully",
  "details": {
    "credits_added": 300,
    "new_balance": 1800,
    "transaction_id": "txn_**********"
  }
}
```

### System Endpoints

#### Health Check

**GET** `/health`

Check API health status.

**Response (200):**

```json
{
  "status": "healthy",
  "timestamp": **********,
  "services": {
    "database": "healthy",
    "assemblyai": "healthy",
    "redis": "healthy"
  },
  "version": "1.0.0",
  "environment": "production"
}
```

## 📱 Flutter Integration Guide

### Setting up HTTP Client

#### 1. Add Dependencies

Add these to your `pubspec.yaml`:

```yaml
dependencies:
  http: ^0.13.5
  shared_preferences: ^2.0.15
```

#### 2. Create API Service Class

Create a service class to handle all API communications:

**Key Components:**

- Base URL configuration
- Token management
- Request/response interceptors
- Error handling
- Automatic token refresh

#### 3. Authentication Flow

Implement the complete authentication flow:

1. **Registration/Login** → Store tokens securely
2. **Token Management** → Auto-refresh expired tokens
3. **API Key Generation** → For background operations
4. **Logout** → Clear stored credentials

#### 4. File Upload for Transcription

Handle multipart file uploads for audio transcription:

**Steps:**

1. Pick audio file using file picker
2. Create multipart request
3. Upload with progress tracking
4. Handle response and store transcription ID

#### 5. Real-time Features

Implement WebSocket connection for streaming:

1. **Connection Setup** → WebSocket to streaming endpoint
2. **Audio Streaming** → Send audio chunks
3. **Real-time Results** → Receive interim and final results
4. **Error Handling** → Connection drops and reconnection

### Error Handling

#### HTTP Status Codes

| Code | Meaning      | Action                    |
| ---- | ------------ | ------------------------- |
| 200  | Success      | Process response          |
| 202  | Accepted     | Check status later        |
| 400  | Bad Request  | Fix request data          |
| 401  | Unauthorized | Refresh token or re-login |
| 403  | Forbidden    | Check permissions         |
| 404  | Not Found    | Resource doesn't exist    |
| 429  | Rate Limited | Wait and retry            |
| 500  | Server Error | Show error message        |

#### Error Response Format

All error responses follow this format:

```json
{
  "error": "Error type",
  "message": "Human readable error message",
  "details": {},
  "timestamp": **********
}
```

#### Rate Limiting Handling

When you receive a 429 response:

1. Check `Retry-After` header
2. Show user-friendly message
3. Implement exponential backoff
4. Queue requests if needed

### Best Practices for Flutter Integration

#### 1. Token Management

- Store tokens in secure storage
- Implement automatic refresh
- Handle token expiration gracefully
- Clear tokens on logout

#### 2. File Handling

- Validate file size before upload
- Support multiple audio formats
- Show upload progress
- Handle large file uploads

#### 3. Offline Support

- Cache transcription results
- Queue requests when offline
- Sync when connection restored
- Show appropriate offline states

#### 4. User Experience

- Show loading states
- Handle errors gracefully
- Implement retry mechanisms
- Provide clear feedback

#### 5. Performance

- Use pagination for history
- Implement proper caching
- Compress large requests
- Handle background processing

## 🔊 Audio Format Support

### Supported Formats

- MP3
- WAV
- M4A
- FLAC
- OGG

### File Size Limits

| Plan       | Max File Size |
| ---------- | ------------- |
| Free       | 25 MB         |
| Basic      | 100 MB        |
| Pro        | 500 MB        |
| Enterprise | 2 GB          |

### Audio Quality Recommendations

- **Sample Rate:** 16kHz or higher
- **Bit Depth:** 16-bit minimum
- **Channels:** Mono or Stereo
- **Duration:** Up to 4 hours per file

## 🚨 Common Integration Issues

### 1. Authentication Issues

**Problem:** 401 Unauthorized errors
**Solution:** Check token validity and refresh if needed

### 2. Rate Limiting

**Problem:** 429 Too Many Requests
**Solution:** Implement proper rate limiting in your app

### 3. File Upload Issues

**Problem:** Large files failing to upload
**Solution:** Check file size limits and implement chunked upload

### 4. WebSocket Connection

**Problem:** Streaming connection drops
**Solution:** Implement reconnection logic with exponential backoff

### 5. Payment Integration

**Problem:** Payment verification failing
**Solution:** Ensure proper signature verification on client side

## 🔗 Webhook Integration

If you want to receive real-time updates in your Flutter app:

### Setting up Webhooks

1. **Transcription Updates** → Get notified when transcription completes
2. **Payment Updates** → Receive payment status changes
3. **Plan Changes** → Get notified of subscription updates

### Webhook Payload Example

```json
{
  "event": "transcription.completed",
  "data": {
    "transcription_id": "trans_123456789",
    "user_id": "abc123def456",
    "status": "completed",
    "result_url": "https://api.yourapp.com/transcribe/trans_123456789"
  },
  "timestamp": **********
}
```

## 📞 Support & Resources

### Rate Limits Reset

Rate limits reset every minute. Monitor headers to avoid hitting limits.

### Testing

Use the development environment (`http://localhost:8000`) for testing.

### Production Checklist

1. ✅ Update base URL to production
2. ✅ Use production API keys
3. ✅ Implement proper error handling
4. ✅ Add request logging
5. ✅ Test payment flows
6. ✅ Verify webhook endpoints

---

**Built for Flutter developers who need reliable transcription services** 🎯
