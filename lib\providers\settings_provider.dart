import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/material.dart';

class SettingsProvider extends ChangeNotifier {
  // Hotkey settings
  List<String> _primaryHotkey = ['Ctrl', 'Win'];
  List<String> _secondaryHotkey = ['Alt', 'F1'];

  // Microphone settings
  String _selectedMicrophone = 'Default Microphone';
  List<String> _availableMicrophones = ['Default Microphone'];
  bool _microphonesLoaded = false;
  double _audioInputLevel = 0.5; // 0.0 to 1.0 (mic sensitivity)

  // System settings
  bool _startWithWindows = false;

  // Theme settings
  ThemeMode _themeMode = ThemeMode.system;

  // Getters
  List<String> get primaryHotkey => _primaryHotkey;
  List<String> get secondaryHotkey => _secondaryHotkey;
  String get selectedMicrophone => _selectedMicrophone;
  List<String> get availableMicrophones => _availableMicrophones;
  double get audioInputLevel => _audioInputLevel;
  bool get startWithWindows => _startWithWindows;
  ThemeMode get themeMode => _themeMode;

  // Constructor
  SettingsProvider() {
    _loadSettings();
    // Defer microphone loading until after the current build is complete
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadAvailableMicrophones();
    });
  }

  // Load settings from SharedPreferences
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Load hotkeys
      final primaryHotkeyString = prefs.getString('primary_hotkey');
      if (primaryHotkeyString != null && primaryHotkeyString.isNotEmpty) {
        _primaryHotkey = primaryHotkeyString.split(',');
      }

      final secondaryHotkeyString = prefs.getString('secondary_hotkey');
      if (secondaryHotkeyString != null && secondaryHotkeyString.isNotEmpty) {
        _secondaryHotkey = secondaryHotkeyString.split(',');
      }

      // Load selected microphone
      _selectedMicrophone =
          prefs.getString('selected_microphone') ?? 'Default Microphone';

      // Load audio input level
      _audioInputLevel = prefs.getDouble('audio_input_level') ?? 0.5;

      // Load start with Windows
      _startWithWindows = prefs.getBool('start_with_windows') ?? false;

      // Load theme mode
      final themeModeString = prefs.getString('theme_mode') ?? 'system';
      _themeMode = _parseThemeMode(themeModeString);

      notifyListeners();
    } catch (e) {
      print('Error loading settings: $e');
    }
  }

  // Save settings to SharedPreferences
  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      await prefs.setString('primary_hotkey', _primaryHotkey.join(','));
      await prefs.setString('secondary_hotkey', _secondaryHotkey.join(','));
      await prefs.setString('selected_microphone', _selectedMicrophone);
      await prefs.setDouble('audio_input_level', _audioInputLevel);
      await prefs.setBool('start_with_windows', _startWithWindows);
      await prefs.setString('theme_mode', _themeMode.name);
    } catch (e) {
      print('Error saving settings: $e');
    }
  }

  // Set primary hotkey
  Future<void> setPrimaryHotkey(List<String> hotkey) async {
    _primaryHotkey = hotkey;
    await _saveSettings();
    notifyListeners();
  }

  // Set secondary hotkey
  Future<void> setSecondaryHotkey(List<String> hotkey) async {
    _secondaryHotkey = hotkey;
    await _saveSettings();
    notifyListeners();
  }

  // Set selected microphone
  Future<void> setSelectedMicrophone(String microphone) async {
    _selectedMicrophone = microphone;
    await _saveSettings();
    notifyListeners();
  }

  // Set audio input level
  Future<void> setAudioInputLevel(double level) async {
    _audioInputLevel = level.clamp(0.0, 1.0);
    await _saveSettings();
    notifyListeners();
  }

  // Set start with Windows
  Future<void> setStartWithWindows(bool enabled) async {
    _startWithWindows = enabled;
    await _saveSettings();
    notifyListeners();

    // TODO: Implement actual Windows startup registry modification
    // This would require platform-specific code to modify Windows registry
    if (enabled) {
      print('🔧 Start with Windows enabled - registry modification needed');
    } else {
      print('🔧 Start with Windows disabled - registry modification needed');
    }
  }

  // Load available microphones (mock implementation)
  Future<void> _loadAvailableMicrophones() async {
    if (_microphonesLoaded) return;

    try {
      // In a real implementation, this would enumerate actual audio devices
      // For now, we'll use mock data
      _availableMicrophones = [
        'Default Microphone',
        'Built-in Microphone',
        'USB Microphone',
        'Bluetooth Headset',
        'External Microphone',
      ];
      _microphonesLoaded = true;

      // Only notify if the microphones list actually changed
      notifyListeners();
    } catch (e) {
      print('Error loading microphones: $e');
    }
  }

  // Get available microphones
  Future<List<String>> getAvailableMicrophones() async {
    if (!_microphonesLoaded) {
      await _loadAvailableMicrophones();
    }
    return _availableMicrophones;
  }

  // Check if hotkey matches current settings
  bool isHotkeyMatch(List<String> pressedKeys) {
    return listEquals(pressedKeys, _primaryHotkey) ||
        listEquals(pressedKeys, _secondaryHotkey);
  }

  // Set theme mode
  Future<void> setThemeMode(ThemeMode themeMode) async {
    _themeMode = themeMode;
    await _saveSettings();
    notifyListeners();
  }

  // Parse theme mode from string
  ThemeMode _parseThemeMode(String themeModeString) {
    switch (themeModeString) {
      case 'light':
        return ThemeMode.light;
      case 'dark':
        return ThemeMode.dark;
      case 'system':
      default:
        return ThemeMode.system;
    }
  }
}
