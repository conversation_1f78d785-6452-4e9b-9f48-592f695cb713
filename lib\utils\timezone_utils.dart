class TimezoneUtils {
  /// Convert a UTC timestamp string to local timezone DateTime
  static DateTime parseToLocal(String? timestamp) {
    if (timestamp == null) return DateTime.now();

    try {
      // Parse the timestamp and convert to local timezone
      final utcDateTime = DateTime.parse(timestamp);
      return utcDateTime.toLocal();
    } catch (e) {
      print('⚠️ Failed to parse timestamp "$timestamp": $e');
      return DateTime.now();
    }
  }

  /// Convert a local DateTime to UTC string for API transmission
  static String toUtcString(DateTime localDateTime) {
    return localDateTime.toUtc().toIso8601String();
  }

  /// Get current time in system timezone
  static DateTime now() {
    return DateTime.now();
  }

  /// Format timestamp for display in system timezone
  static String formatForDisplay(DateTime dateTime, {bool includeTime = true}) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays == 0) {
      if (includeTime) {
        return 'Today ${_formatTime(dateTime)}';
      } else {
        return 'Today';
      }
    } else if (difference.inDays == 1) {
      if (includeTime) {
        return 'Yesterday ${_formatTime(dateTime)}';
      } else {
        return 'Yesterday';
      }
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      if (includeTime) {
        return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${_formatTime(dateTime)}';
      } else {
        return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
      }
    }
  }

  /// Format time in 24-hour format
  static String _formatTime(DateTime dateTime) {
    final hour = dateTime.hour.toString().padLeft(2, '0');
    final minute = dateTime.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  /// Format time in 12-hour format
  static String formatTime12Hour(DateTime dateTime) {
    final hour = dateTime.hour == 0
        ? 12
        : dateTime.hour > 12
        ? dateTime.hour - 12
        : dateTime.hour;
    final minute = dateTime.minute.toString().padLeft(2, '0');
    final period = dateTime.hour < 12 ? 'AM' : 'PM';
    return '$hour:$minute $period';
  }

  /// Get timezone offset string (e.g., "+05:30", "-08:00")
  static String getTimezoneOffset() {
    final now = DateTime.now();
    final offset = now.timeZoneOffset;
    final hours = offset.inHours.abs().toString().padLeft(2, '0');
    final minutes = (offset.inMinutes.abs() % 60).toString().padLeft(2, '0');
    final sign = offset.isNegative ? '-' : '+';
    return '$sign$hours:$minutes';
  }

  /// Get system timezone name
  static String getTimezoneName() {
    return DateTime.now().timeZoneName;
  }
}
