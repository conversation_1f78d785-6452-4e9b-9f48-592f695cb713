import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/transcription_provider.dart';
import '../providers/auth_provider.dart';
import '../providers/settings_provider.dart';
import '../services/clipboard_service.dart';
import '../services/transcription_api_service.dart';
import '../services/api_config.dart';
import '../services/rate_limit_detector.dart';
import '../widgets/siri_waveform_button.dart';

// Keep original VoiceAssistantScreen for backward compatibility
class VoiceAssistantScreen extends StatelessWidget {
  const VoiceAssistantScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Scaffold(
      backgroundColor: colorScheme.surface,
      body: Consumer<TranscriptionProvider>(
        builder: (context, provider, child) {
          return Column(
            children: [
              // Top bar
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 16,
                ),
                decoration: BoxDecoration(
                  color: colorScheme.surfaceContainerLow,
                  border: Border(
                    bottom: BorderSide(color: colorScheme.outline, width: 1),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(Icons.mic, color: colorScheme.primary, size: 24),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'MurMur AI',
                            style: Theme.of(context).textTheme.headlineSmall
                                ?.copyWith(color: colorScheme.onSurface),
                          ),
                          Consumer<AuthProvider>(
                            builder: (context, authProvider, child) {
                              final user = authProvider.user;
                              return GestureDetector(
                                onTap: () {
                                  // Manual refresh credits when user taps
                                  authProvider.refreshCreditsNow();
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text('🔄 Refreshing credits...'),
                                      duration: Duration(seconds: 1),
                                      backgroundColor: Colors.blue,
                                    ),
                                  );
                                },
                                child: Text(
                                  user != null
                                      ? '${user.fullName} • ${user.plan.toUpperCase()} • ${user.creditsRemaining} credits (tap to refresh)'
                                      : 'Welcome!',
                                  style: Theme.of(context).textTheme.labelSmall
                                      ?.copyWith(
                                        color: colorScheme.onSurfaceVariant,
                                      ),
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                    // Streaming mode toggle
                    Row(
                      children: [
                        Text(
                          'Stream',
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(color: colorScheme.onSurfaceVariant),
                        ),
                        const SizedBox(width: 8),
                        Switch(
                          value: provider.useStreaming,
                          onChanged: provider.isRecording
                              ? null
                              : provider.setUseStreaming,
                          activeColor: Colors.green,
                        ),
                      ],
                    ),

                    const SizedBox(width: 16),

                    // Server status indicator
                    FutureBuilder<bool>(
                      future: ApiConfig.checkServerStatus(),
                      builder: (context, snapshot) {
                        final isOnline = snapshot.data ?? true;
                        return Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: isOnline
                                ? Colors.green.withOpacity(0.2)
                                : Colors.red.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                isOnline ? Icons.cloud_done : Icons.cloud_off,
                                size: 14,
                                color: isOnline ? Colors.green : Colors.red,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                isOnline ? 'Online' : 'Offline',
                                style: Theme.of(context).textTheme.labelSmall
                                    ?.copyWith(
                                      color: isOnline
                                          ? Colors.green
                                          : Colors.red,
                                      fontWeight: FontWeight.w500,
                                    ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),

                    const SizedBox(width: 16),
                    // Logout button
                    Consumer<AuthProvider>(
                      builder: (context, authProvider, child) {
                        return IconButton(
                          onPressed: () async {
                            await authProvider.logout();
                          },
                          icon: Icon(
                            Icons.logout,
                            color: colorScheme.onSurfaceVariant,
                          ),
                          tooltip: 'Logout',
                        );
                      },
                    ),
                  ],
                ),
              ),

              // Main content area
              Expanded(child: VoiceAssistantContent()),
            ],
          );
        },
      ),
    );
  }
}

// Extracted content component for use in tabs
class VoiceAssistantContent extends StatefulWidget {
  const VoiceAssistantContent({super.key});

  @override
  State<VoiceAssistantContent> createState() => _VoiceAssistantContentState();
}

class _VoiceAssistantContentState extends State<VoiceAssistantContent> {
  final ScrollController _streamingScrollController = ScrollController();
  final ScrollController _mainScrollController = ScrollController();
  String _lastStreamingText = '';
  String _lastTranscriptionText = '';

  @override
  void dispose() {
    _streamingScrollController.dispose();
    _mainScrollController.dispose();
    super.dispose();
  }

  void _scrollToBottom(ScrollController controller) {
    if (controller.hasClients) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        controller.animateTo(
          controller.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Consumer<TranscriptionProvider>(
      builder: (context, provider, child) {
        // Check if streaming text has changed and auto-scroll
        if (provider.streamingDisplayText != _lastStreamingText) {
          _lastStreamingText = provider.streamingDisplayText;
          if (provider.streamingDisplayText.isNotEmpty) {
            _scrollToBottom(_streamingScrollController);
          }
        }

        // Check if transcription text has changed and auto-scroll
        if (provider.transcriptionText != _lastTranscriptionText) {
          _lastTranscriptionText = provider.transcriptionText;
          if (provider.transcriptionText.isNotEmpty) {
            _scrollToBottom(_mainScrollController);
          }
        }

        return Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Controls row
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Streaming mode toggle
                  Row(
                    children: [
                      Text(
                        'Stream',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: colorScheme.onSurfaceVariant,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Switch(
                        value: provider.useStreaming,
                        onChanged: provider.isRecording
                            ? null
                            : provider.setUseStreaming,
                        activeColor: Colors.green,
                      ),
                    ],
                  ),
                ],
              ),

              const SizedBox(height: 40),

              // Status indicator
              _buildStatusIndicator(context, provider),

              const SizedBox(height: 40),

              // Record button
              _buildRecordButton(context, provider, colorScheme),

              const SizedBox(height: 20),

              // File picker button (only show when not recording and not streaming mode)
              if (!provider.isRecording && !provider.useStreaming)
                _buildFilePickerButton(context, provider, colorScheme),

              // Upload button (only show when file is selected and not processing)
              if (!provider.isRecording &&
                  !provider.useStreaming &&
                  provider.hasSelectedFile &&
                  !provider.isProcessing)
                _buildUploadButton(context, provider, colorScheme),

              // Test connection button (show when there's an error)
              if (provider.errorMessage.isNotEmpty)
                _buildTestConnectionButton(context, provider, colorScheme),

              const SizedBox(height: 40),

              // Transcription result
              Expanded(
                child: _buildTranscriptionArea(context, provider, colorScheme),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatusIndicator(
    BuildContext context,
    TranscriptionProvider provider,
  ) {
    String statusText;
    Color statusColor;
    IconData statusIcon;

    switch (provider.status) {
      case TranscriptionStatus.idle:
        statusText = provider.useStreaming
            ? 'Ready for streaming transcription'
            : 'Ready to record';
        statusColor = Colors.grey;
        statusIcon = Icons.mic_off;
        break;
      case TranscriptionStatus.recording:
        statusText = 'Recording... Click to stop';
        statusColor = Colors.red;
        statusIcon = Icons.mic;
        break;
      case TranscriptionStatus.streaming:
        statusText = 'Streaming... Real-time transcription active';
        statusColor = Colors.green;
        statusIcon = Icons.stream;
        break;
      case TranscriptionStatus.processing:
        statusText = 'Processing transcription...';
        statusColor = Colors.orange;
        statusIcon = Icons.hourglass_empty;
        break;
      case TranscriptionStatus.uploading:
        statusText = 'Uploading audio file...';
        statusColor = Colors.blue;
        statusIcon = Icons.cloud_upload;
        break;
      case TranscriptionStatus.completed:
        if (provider.useStreaming) {
          statusText = provider.lastProcessingTime.isNotEmpty
              ? 'Streaming completed (${provider.lastProcessingTime})'
              : 'Streaming completed';
        } else {
          statusText = provider.lastProcessingTime.isNotEmpty
              ? 'Transcription completed (${provider.lastProcessingTime})'
              : 'Transcription completed';
        }
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        break;
      case TranscriptionStatus.error:
        final isRateLimited =
            provider.errorMessage.toLowerCase().contains('server is busy') ||
            provider.errorMessage.toLowerCase().contains('rate limit');

        statusText = provider.lastProcessingTime.isNotEmpty
            ? '${isRateLimited ? 'Server is busy' : 'Something went wrong'} (${provider.lastProcessingTime})'
            : '${isRateLimited ? 'Server is busy' : 'Something went wrong'}';
        statusColor = isRateLimited ? Colors.orange : Colors.red;
        statusIcon = isRateLimited
            ? Icons.hourglass_empty
            : Icons.error_outline;
        break;
    }

    return Consumer<SettingsProvider>(
      builder: (context, settingsProvider, child) {
        return Column(
          children: [
            Icon(statusIcon, color: statusColor, size: 48),
            const SizedBox(height: 12),
            Text(
              statusText,
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(color: statusColor),
              textAlign: TextAlign.center,
            ),
            if (provider.useStreaming && provider.isStreaming) ...[
              const SizedBox(height: 8),
              Text(
                '🟢 Real-time mode active',
                style: Theme.of(
                  context,
                ).textTheme.labelMedium?.copyWith(color: Colors.green.shade300),
              ),
            ],
            if (provider.useStreaming && provider.globalHotkeyEnabled) ...[
              const SizedBox(height: 8),
              Text(
                '⌨️ Global hotkey: ${settingsProvider.primaryHotkey.join('+')} (hold to record)',
                style: Theme.of(context).textTheme.labelMedium?.copyWith(
                  color: Colors.purple.shade300,
                ),
              ),
            ],
          ],
        );
      },
    );
  }

  Widget _buildRecordButton(
    BuildContext context,
    TranscriptionProvider provider,
    ColorScheme colorScheme,
  ) {
    Color buttonColor;

    if (provider.isStreaming) {
      buttonColor = Colors.green.shade600;
    } else if (provider.isRecording) {
      buttonColor = Colors.red.shade600;
    } else {
      buttonColor = provider.useStreaming
          ? Colors.green.shade600
          : Colors.blue.shade600;
    }

    return SiriWaveformButton(
      color: buttonColor,
      isRecording: provider.isRecording,
      isStreaming: provider.isStreaming,
      isProcessing: provider.isProcessing,
      size: 120.0, // Properly sized for elegant UI
      onTap: provider.isProcessing
          ? null
          : () async {
              if (provider.isRecording) {
                if (provider.useStreaming) {
                  await provider.stopStreamingRecording();
                } else {
                  await provider.stopRecording();
                }
              } else {
                if (provider.useStreaming) {
                  await provider.startStreamingRecording();
                } else {
                  await provider.startRecording();
                }
              }
            },
    );
  }

  Widget _buildFilePickerButton(
    BuildContext context,
    TranscriptionProvider provider,
    ColorScheme colorScheme,
  ) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          'or',
          style: Theme.of(
            context,
          ).textTheme.bodyLarge?.copyWith(color: Colors.grey),
        ),
        const SizedBox(width: 20),
        GestureDetector(
          onTap: () => provider.pickAudioFile(),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.orange.shade600,
              borderRadius: BorderRadius.circular(25),
              boxShadow: [
                BoxShadow(
                  color: Colors.orange.shade600.withOpacity(0.3),
                  blurRadius: 15,
                  spreadRadius: 0,
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.folder_open, color: colorScheme.onPrimary, size: 20),
                const SizedBox(width: 8),
                Text(
                  'Pick Audio File',
                  style: Theme.of(context).textTheme.labelLarge?.copyWith(
                    color: colorScheme.onPrimary,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildUploadButton(
    BuildContext context,
    TranscriptionProvider provider,
    ColorScheme colorScheme,
  ) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          'then',
          style: Theme.of(
            context,
          ).textTheme.bodyLarge?.copyWith(color: Colors.grey),
        ),
        const SizedBox(width: 20),
        GestureDetector(
          onTap: provider.isProcessing
              ? null
              : () => provider.uploadAndTranscribe(),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.purple.shade600,
              borderRadius: BorderRadius.circular(25),
              boxShadow: [
                BoxShadow(
                  color: Colors.purple.shade600.withOpacity(0.3),
                  blurRadius: 15,
                  spreadRadius: 0,
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.upload_file, color: colorScheme.onPrimary, size: 20),
                const SizedBox(width: 8),
                Text(
                  'Upload & Transcribe',
                  style: Theme.of(context).textTheme.labelLarge?.copyWith(
                    color: colorScheme.onPrimary,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTestConnectionButton(
    BuildContext context,
    TranscriptionProvider provider,
    ColorScheme colorScheme,
  ) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const SizedBox(height: 20),
        ElevatedButton.icon(
          onPressed: () async {
            final result = await TranscriptionApiService.testConnection();
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  result['success']
                      ? 'Server is running!'
                      : 'Server error: ${result['error']}',
                ),
                backgroundColor: result['success'] ? Colors.green : Colors.red,
              ),
            );
          },
          icon: Icon(Icons.network_ping),
          label: Text('Test Server Connection'),
          style: ElevatedButton.styleFrom(
            backgroundColor: colorScheme.primary,
            foregroundColor: colorScheme.onPrimary,
            padding: EdgeInsets.symmetric(horizontal: 20, vertical: 12),
          ),
        ),
      ],
    );
  }

  Widget _buildErrorCard(
    BuildContext context,
    TranscriptionProvider provider,
    ColorScheme colorScheme,
  ) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final userPlan = authProvider.user?.plan ?? 'free';
        final isRateLimited =
            provider.errorMessage.toLowerCase().contains('server is busy') ||
            provider.errorMessage.toLowerCase().contains('rate limit') ||
            provider.errorMessage.toLowerCase().contains('429') ||
            provider.errorMessage.toLowerCase().contains('too many');

        // Get specific rate limit information
        RateLimitInfo? rateLimitInfo;
        if (isRateLimited) {
          rateLimitInfo = RateLimitDetector.instance.parseRateLimitError(
            provider.errorMessage,
            userPlan: userPlan,
          );
        }

        return Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: isRateLimited
                ? Colors.orange.withOpacity(0.08)
                : Colors.red.withOpacity(0.08),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: isRateLimited
                  ? Colors.orange.withOpacity(0.2)
                  : Colors.red.withOpacity(0.2),
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: isRateLimited
                          ? Colors.orange.withOpacity(0.15)
                          : Colors.red.withOpacity(0.15),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      isRateLimited
                          ? Icons.hourglass_empty
                          : Icons.error_outline,
                      color: isRateLimited
                          ? Colors.orange[700]
                          : Colors.red[700],
                      size: 18,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          rateLimitInfo?.title ??
                              (isRateLimited
                                  ? 'Server is Busy'
                                  : 'Something went wrong'),
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(
                                color: isRateLimited
                                    ? Colors.orange[800]
                                    : Colors.red[800],
                                fontWeight: FontWeight.w600,
                              ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          rateLimitInfo?.description ??
                              _getErrorDescription(provider.errorMessage),
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(
                                color: isRateLimited
                                    ? Colors.orange[700]
                                    : Colors.red[700],
                                height: 1.3,
                              ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              // Show specific limit information for rate limiting
              if (rateLimitInfo != null) ...[
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.orange.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.orange.withOpacity(0.2)),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.info_outline,
                            color: Colors.orange[700],
                            size: 16,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Your Current Limit',
                            style: Theme.of(context).textTheme.labelMedium
                                ?.copyWith(
                                  color: Colors.orange[700],
                                  fontWeight: FontWeight.w600,
                                ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        rateLimitInfo.limit,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.orange[600],
                        ),
                      ),
                      if (rateLimitInfo.waitTime != 'N/A' &&
                          rateLimitInfo.waitTime !=
                              'Until current transcriptions complete') ...[
                        const SizedBox(height: 4),
                        Text(
                          'Reset time: ${rateLimitInfo.waitTime}',
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(
                                color: Colors.orange[600],
                                fontStyle: FontStyle.italic,
                              ),
                        ),
                      ],
                    ],
                  ),
                ),
              ],

              const SizedBox(height: 16),
              Column(
                children: [
                  // Try Again Button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: () => provider.clearTranscription(),
                      icon: const Icon(Icons.refresh, size: 18),
                      label: const Text('Try Again'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: isRateLimited
                            ? Colors.orange[600]
                            : Colors.red[600],
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                        elevation: 0,
                      ),
                    ),
                  ),

                  // Upgrade suggestion for rate limiting
                  if (rateLimitInfo != null && rateLimitInfo.canUpgrade) ...[
                    const SizedBox(height: 8),
                    SizedBox(
                      width: double.infinity,
                      child: TextButton.icon(
                        onPressed: () {
                          // Navigate to plans page
                          Navigator.pushNamed(context, '/plans');
                        },
                        icon: const Icon(Icons.upgrade, size: 16),
                        label: const Text('Upgrade Plan'),
                        style: TextButton.styleFrom(
                          foregroundColor: Colors.blue[700],
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 8,
                          ),
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  String _getErrorDescription(String errorMessage) {
    final lowerMessage = errorMessage.toLowerCase();

    if (lowerMessage.contains('server is busy') ||
        lowerMessage.contains('rate limit')) {
      return 'The server is handling a lot of requests right now. Please wait a moment and try again.';
    } else if (lowerMessage.contains('network') ||
        lowerMessage.contains('connection')) {
      return 'Unable to connect to the server. Please check your internet connection.';
    } else if (lowerMessage.contains('timeout')) {
      return 'The request took too long. This might be due to a slow internet connection.';
    } else if (lowerMessage.contains('authentication') ||
        lowerMessage.contains('unauthorized')) {
      return 'Your session has expired. Please log out and log back in.';
    } else if (lowerMessage.contains('file') ||
        lowerMessage.contains('audio')) {
      return 'There was an issue with the audio file. Please try recording again.';
    } else if (lowerMessage.contains('server error')) {
      return 'The server is experiencing technical difficulties. Please try again in a few moments.';
    } else {
      return 'An unexpected error occurred. Please try again or contact support if the problem persists.';
    }
  }

  Widget _buildTranscriptionArea(
    BuildContext context,
    TranscriptionProvider provider,
    ColorScheme colorScheme,
  ) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerLow,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: colorScheme.outline),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Streaming area (only show when streaming)
          if (provider.useStreaming && provider.isStreaming) ...[
            Row(
              children: [
                Text(
                  'Live Stream',
                  style: Theme.of(
                    context,
                  ).textTheme.titleMedium?.copyWith(color: Colors.green),
                ),
                const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 6,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    'RECORDING',
                    style: Theme.of(context).textTheme.labelSmall?.copyWith(
                      color: Colors.green,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Expanded(
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green.withOpacity(0.3)),
                ),
                child: SingleChildScrollView(
                  controller: _streamingScrollController,
                  child: Text(
                    provider.streamingDisplayText,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: provider.streamingText.isEmpty
                          ? Colors.green.shade300
                          : colorScheme.onSurface,
                      height: 1.4,
                      fontStyle: provider.streamingText.isEmpty
                          ? FontStyle.italic
                          : FontStyle.normal,
                    ),
                  ),
                ),
              ),
            ),
          ],

          // Main transcription area (hide during active streaming)
          if (!(provider.useStreaming && provider.isStreaming)) ...[
            Row(
              children: [
                Text(
                  provider.useStreaming
                      ? 'Final Transcription'
                      : 'Transcription',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: colorScheme.onSurface,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                if (provider.transcriptionText.isNotEmpty) ...[
                  IconButton(
                    icon: Icon(Icons.copy, color: colorScheme.primary),
                    onPressed: () async {
                      await ClipboardService.copyToClipboard(
                        provider.transcriptionText,
                      );
                    },
                    tooltip: 'Copy to clipboard',
                  ),
                  IconButton(
                    icon: Icon(Icons.content_paste, color: colorScheme.primary),
                    onPressed: () async {
                      await ClipboardService.copyAndPaste(
                        provider.transcriptionText,
                      );
                    },
                    tooltip: 'Paste text',
                  ),
                ],
              ],
            ),
            const SizedBox(height: 12),
            Expanded(
              child: SingleChildScrollView(
                controller: _mainScrollController,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Show error message if there is one
                    if (provider.errorMessage.isNotEmpty) ...[
                      _buildErrorCard(context, provider, colorScheme),
                      const SizedBox(height: 16),
                    ],

                    // Transcription text
                    Text(
                      provider.transcriptionText.isEmpty
                          ? (provider.useStreaming
                                ? 'Completed transcriptions will appear here...'
                                : 'Your transcribed text will appear here...')
                          : provider.transcriptionText,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: provider.transcriptionText.isEmpty
                            ? colorScheme.onSurfaceVariant
                            : colorScheme.onSurface,
                        height: 1.5,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}
