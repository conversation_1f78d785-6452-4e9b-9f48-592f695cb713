# Flutter Transcription API Documentation

This documentation provides a complete guide for integrating the FastAPI transcription service with your Flutter desktop application.

## Table of Contents

- [Overview](#overview)
- [Setup & Dependencies](#setup--dependencies)
- [API Configuration](#api-configuration)
- [Authentication](#authentication)
- [Audio Upload](#audio-upload)
- [Transcription Workflow](#transcription-workflow)
- [Real-Time Streaming](#real-time-streaming)
- [Error Handling](#error-handling)
- [Complete Example](#complete-example)
- [Streaming Example](#streaming-example)
- [API Reference](#api-reference)

## Overview

The transcription API provides both file-based and real-time audio transcription using AssemblyAI with the following features:

- **Authentication**: JWT-based user authentication
- **File Upload**: Secure audio file upload to AssemblyAI
- **Transcription**: Advanced transcription with speaker detection, sentiment analysis, and more
- **Real-time Status**: Live transcription progress tracking
- **Real-time Streaming**: Live audio streaming with instant transcription results
- **WebSocket Support**: Real-time bidirectional communication for streaming
- **Results Retrieval**: Detailed transcription results with timestamps

## Setup & Dependencies

Add these dependencies to your `pubspec.yaml`:

```yaml
dependencies:
  flutter:
    sdk: flutter
  http: ^1.1.0
  dio: ^5.3.2 # Alternative HTTP client with better file upload support
  file_picker: ^6.1.1 # For file selection
  shared_preferences: ^2.2.2 # For token storage
  provider: ^6.1.1 # For state management
  web_socket_channel: ^2.4.0 # For real-time streaming
  record: ^5.0.4 # For audio recording
  permission_handler: ^11.0.1 # For microphone permissions
  path_provider: ^2.1.1 # For file storage
```

## API Configuration

Create a configuration class for your API settings:

```dart
// lib/services/api_config.dart
class ApiConfig {
  static const String baseUrl = 'http://localhost:8000';
  static const String apiVersion = 'v1';

  // Endpoints
  static const String loginEndpoint = '/login';
  static const String registerEndpoint = '/register';
  static const String uploadEndpoint = '/api/$apiVersion/transcription/upload';
  static const String submitEndpoint = '/api/$apiVersion/transcription/submit';
  static const String statusEndpoint = '/api/$apiVersion/transcription';
  static const String historyEndpoint = '/api/$apiVersion/transcription/history';

  // Streaming endpoints
  static const String streamingSessionEndpoint = '/api/$apiVersion/streaming/session';
  static const String streamingWebSocketEndpoint = '/api/$apiVersion/streaming/ws';
  static const String streamingActiveSessionsEndpoint = '/api/$apiVersion/streaming/sessions/active';

  // Timeout settings
  static const Duration connectTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 60);
}
```

## Authentication

### Auth Service

```dart
// lib/services/auth_service.dart
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'api_config.dart';

class AuthService {
  static const String _tokenKey = 'access_token';
  static const String _userEmailKey = 'user_email';

  // Login
  static Future<Map<String, dynamic>> login(String email, String password) async {
    try {
      final response = await http.post(
        Uri.parse('${ApiConfig.baseUrl}${ApiConfig.loginEndpoint}'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'email': email,
          'password': password,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        await _saveToken(data['access_token']);
        await _saveUserEmail(email);
        return {'success': true, 'data': data};
      } else {
        final error = jsonDecode(response.body);
        return {'success': false, 'error': error['detail'] ?? 'Login failed'};
      }
    } catch (e) {
      return {'success': false, 'error': 'Network error: $e'};
    }
  }

  // Register
  static Future<Map<String, dynamic>> register({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    String? company,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('${ApiConfig.baseUrl}${ApiConfig.registerEndpoint}'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'email': email,
          'password': password,
          'first_name': firstName,
          'last_name': lastName,
          'company': company,
        }),
      );

      if (response.statusCode == 201) {
        final data = jsonDecode(response.body);
        return {'success': true, 'data': data};
      } else {
        final error = jsonDecode(response.body);
        return {'success': false, 'error': error['detail'] ?? 'Registration failed'};
      }
    } catch (e) {
      return {'success': false, 'error': 'Network error: $e'};
    }
  }

  // Token management
  static Future<void> _saveToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_tokenKey, token);
  }

  static Future<String?> getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_tokenKey);
  }

  static Future<void> _saveUserEmail(String email) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userEmailKey, email);
  }

  static Future<String?> getUserEmail() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_userEmailKey);
  }

  static Future<void> logout() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_tokenKey);
    await prefs.remove(_userEmailKey);
  }

  static Future<bool> isLoggedIn() async {
    final token = await getToken();
    return token != null;
  }

  // Get authorization headers
  static Future<Map<String, String>> getAuthHeaders() async {
    final token = await getToken();
    return {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $token',
    };
  }
}
```

## Audio Upload

### File Upload Service

```dart
// lib/services/upload_service.dart
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:file_picker/file_picker.dart';
import 'auth_service.dart';
import 'api_config.dart';

class UploadService {
  static final Dio _dio = Dio(BaseOptions(
    connectTimeout: ApiConfig.connectTimeout,
    receiveTimeout: ApiConfig.receiveTimeout,
  ));

  // Pick audio file
  static Future<File?> pickAudioFile() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowedExtensions: ['mp3', 'wav', 'flac', 'm4a', 'aac', 'ogg', 'wma', 'amr', '3gp', 'mp4', 'mov', 'avi'],
    );

    if (result != null && result.files.single.path != null) {
      return File(result.files.single.path!);
    }
    return null;
  }

  // Upload file to API
  static Future<Map<String, dynamic>> uploadFile(File file) async {
    try {
      final token = await AuthService.getToken();
      if (token == null) {
        return {'success': false, 'error': 'Not authenticated'};
      }

      // Create form data
      FormData formData = FormData.fromMap({
        'file': await MultipartFile.fromFile(
          file.path,
          filename: file.path.split('/').last,
        ),
      });

      final response = await _dio.post(
        '${ApiConfig.baseUrl}${ApiConfig.uploadEndpoint}',
        data: formData,
        options: Options(
          headers: {'Authorization': 'Bearer $token'},
        ),
        onSendProgress: (sent, total) {
          // Optional: Handle upload progress
          print('Upload progress: ${(sent / total * 100).toStringAsFixed(2)}%');
        },
      );

      if (response.statusCode == 200) {
        return {'success': true, 'data': response.data};
      } else {
        return {'success': false, 'error': 'Upload failed'};
      }
    } on DioException catch (e) {
      return {'success': false, 'error': 'Network error: ${e.message}'};
    } catch (e) {
      return {'success': false, 'error': 'Unexpected error: $e'};
    }
  }
}
```

## Transcription Workflow

### Transcription Service

```dart
// lib/services/transcription_service.dart
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'auth_service.dart';
import 'api_config.dart';

class TranscriptionOptions {
  final String? languageCode;
  final bool punctuate;
  final bool formatText;
  final bool speakerLabels;
  final bool sentimentAnalysis;
  final bool entityDetection;
  final bool autoChapters;
  final bool autoHighlights;
  final bool summarization;

  TranscriptionOptions({
    this.languageCode = 'en',
    this.punctuate = true,
    this.formatText = true,
    this.speakerLabels = false,
    this.sentimentAnalysis = false,
    this.entityDetection = false,
    this.autoChapters = false,
    this.autoHighlights = false,
    this.summarization = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'language_code': languageCode,
      'punctuate': punctuate,
      'format_text': formatText,
      'speaker_labels': speakerLabels,
      'sentiment_analysis': sentimentAnalysis,
      'entity_detection': entityDetection,
      'auto_chapters': autoChapters,
      'auto_highlights': autoHighlights,
      'summarization': summarization,
    };
  }
}

class TranscriptionService {
  // Submit transcription
  static Future<Map<String, dynamic>> submitTranscription({
    required String audioUrl,
    required String audioFilename,
    double? estimatedDurationMinutes,
    TranscriptionOptions? options,
  }) async {
    try {
      final headers = await AuthService.getAuthHeaders();

      final response = await http.post(
        Uri.parse('${ApiConfig.baseUrl}${ApiConfig.submitEndpoint}'),
        headers: headers,
        body: jsonEncode({
          'audio_url': audioUrl,
          'audio_filename': audioFilename,
          'estimated_duration_minutes': estimatedDurationMinutes,
          'options': options?.toJson(),
        }),
      );

      if (response.statusCode == 201) {
        final data = jsonDecode(response.body);
        return {'success': true, 'data': data};
      } else {
        final error = jsonDecode(response.body);
        return {'success': false, 'error': error['detail'] ?? 'Submission failed'};
      }
    } catch (e) {
      return {'success': false, 'error': 'Network error: $e'};
    }
  }

  // Check transcription status
  static Future<Map<String, dynamic>> getTranscriptionStatus(int transcriptionId) async {
    try {
      final headers = await AuthService.getAuthHeaders();

      final response = await http.get(
        Uri.parse('${ApiConfig.baseUrl}${ApiConfig.statusEndpoint}/$transcriptionId/status'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return {'success': true, 'data': data};
      } else {
        final error = jsonDecode(response.body);
        return {'success': false, 'error': error['detail'] ?? 'Status check failed'};
      }
    } catch (e) {
      return {'success': false, 'error': 'Network error: $e'};
    }
  }

  // Get transcription results
  static Future<Map<String, dynamic>> getTranscriptionResult(int transcriptionId) async {
    try {
      final headers = await AuthService.getAuthHeaders();

      final response = await http.get(
        Uri.parse('${ApiConfig.baseUrl}${ApiConfig.statusEndpoint}/$transcriptionId/result'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return {'success': true, 'data': data};
      } else {
        final error = jsonDecode(response.body);
        return {'success': false, 'error': error['detail'] ?? 'Result retrieval failed'};
      }
    } catch (e) {
      return {'success': false, 'error': 'Network error: $e'};
    }
  }

  // Get transcription history
  static Future<Map<String, dynamic>> getTranscriptionHistory({
    int skip = 0,
    int limit = 20,
    String? status,
  }) async {
    try {
      final headers = await AuthService.getAuthHeaders();

      final queryParams = <String, String>{
        'skip': skip.toString(),
        'limit': limit.toString(),
      };
      if (status != null) queryParams['status'] = status;

      final uri = Uri.parse('${ApiConfig.baseUrl}${ApiConfig.historyEndpoint}')
          .replace(queryParameters: queryParams);

      final response = await http.get(uri, headers: headers);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return {'success': true, 'data': data};
      } else {
        final error = jsonDecode(response.body);
        return {'success': false, 'error': error['detail'] ?? 'History retrieval failed'};
      }
    } catch (e) {
      return {'success': false, 'error': 'Network error: $e'};
    }
  }
}
```

## Real-Time Streaming

### Streaming Configuration

```dart
// lib/models/streaming_options.dart
class StreamingOptions {
  final int sampleRate;
  final String encoding;
  final int channels;
  final bool interimResults;
  final bool punctuate;
  final bool formatText;
  final bool speakerLabels;
  final String languageCode;
  final bool redactPii;

  StreamingOptions({
    this.sampleRate = 16000,
    this.encoding = 'pcm_s16le',
    this.channels = 1,
    this.interimResults = true,
    this.punctuate = true,
    this.formatText = true,
    this.speakerLabels = false,
    this.languageCode = 'en',
    this.redactPii = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'sample_rate': sampleRate,
      'encoding': encoding,
      'channels': channels,
      'interim_results': interimResults,
      'punctuate': punctuate,
      'format_text': formatText,
      'speaker_labels': speakerLabels,
      'language_code': languageCode,
      'redact_pii': redactPii,
    };
  }
}
```

### Streaming Service

```dart
// lib/services/streaming_service.dart
import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:http/http.dart' as http;
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:record/record.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:path_provider/path_provider.dart';
import 'auth_service.dart';
import 'api_config.dart';
import '../models/streaming_options.dart';

class StreamingTranscriptionService {
  WebSocketChannel? _channel;
  final AudioRecorder _recorder = AudioRecorder();
  StreamController<Map<String, dynamic>>? _transcriptionController;
  String? _sessionId;
  bool _isStreaming = false;
  Timer? _recordingTimer;

  Stream<Map<String, dynamic>>? get transcriptionStream =>
      _transcriptionController?.stream;

  bool get isStreaming => _isStreaming;
  String? get sessionId => _sessionId;

  // Create streaming session
  Future<Map<String, dynamic>> createSession({
    StreamingOptions? options,
  }) async {
    try {
      final token = await AuthService.getToken();
      if (token == null) {
        return {'success': false, 'error': 'Not authenticated'};
      }

      final headers = await AuthService.getAuthHeaders();
      final response = await http.post(
        Uri.parse('${ApiConfig.baseUrl}${ApiConfig.streamingSessionEndpoint}'),
        headers: headers,
        body: jsonEncode({
          'access_token': token,
          'options': options?.toJson() ?? StreamingOptions().toJson(),
        }),
      );

      if (response.statusCode == 201 || response.statusCode == 200) {
        final data = jsonDecode(response.body);
        _sessionId = data['session_id'];
        return {'success': true, 'data': data};
      } else {
        final error = jsonDecode(response.body);
        return {'success': false, 'error': error['detail'] ?? 'Session creation failed'};
      }
    } catch (e) {
      return {'success': false, 'error': 'Network error: $e'};
    }
  }

  // Start streaming transcription
  Future<Map<String, dynamic>> startStreaming() async {
    try {
      if (_sessionId == null) {
        return {'success': false, 'error': 'No session created'};
      }

      // Check microphone permission
      if (!await _checkMicrophonePermission()) {
        return {'success': false, 'error': 'Microphone permission denied'};
      }

      final token = await AuthService.getToken();
      if (token == null) {
        return {'success': false, 'error': 'Not authenticated'};
      }

      // Connect to WebSocket
      final wsUrl = '${ApiConfig.baseUrl.replaceAll('http', 'ws')}${ApiConfig.streamingWebSocketEndpoint}/$_sessionId?token=$token';

      _channel = WebSocketChannel.connect(Uri.parse(wsUrl));
      _transcriptionController = StreamController<Map<String, dynamic>>.broadcast();

      // Listen for messages
      _channel!.stream.listen(
        (message) {
          try {
            final data = jsonDecode(message);
            _transcriptionController?.add(data);
          } catch (e) {
            print('Error parsing WebSocket message: $e');
          }
        },
        onError: (error) {
          print('WebSocket error: $error');
          _transcriptionController?.addError(error);
        },
        onDone: () {
          print('WebSocket connection closed');
          _stopStreaming();
        },
      );

      // Start audio recording
      await _startAudioRecording();

      _isStreaming = true;
      return {'success': true, 'message': 'Streaming started'};

    } catch (e) {
      return {'success': false, 'error': 'Failed to start streaming: $e'};
    }
  }

  // Stop streaming
  Future<void> stopStreaming() async {
    await _stopStreaming();
  }

  Future<void> _stopStreaming() async {
    _isStreaming = false;
    _recordingTimer?.cancel();

    // Stop recording
    if (await _recorder.isRecording()) {
      await _recorder.stop();
    }

    // Send stop command
    if (_channel != null) {
      _channel!.sink.add(jsonEncode({'type': 'stop'}));
      await _channel!.sink.close();
      _channel = null;
    }

    await _transcriptionController?.close();
    _transcriptionController = null;
    _sessionId = null;
  }

  // Pause streaming
  Future<void> pauseStreaming() async {
    if (_channel != null && _isStreaming) {
      _channel!.sink.add(jsonEncode({'type': 'pause'}));
    }
  }

  // Resume streaming
  Future<void> resumeStreaming() async {
    if (_channel != null && _isStreaming) {
      _channel!.sink.add(jsonEncode({'type': 'resume'}));
    }
  }

  // Check microphone permission
  Future<bool> _checkMicrophonePermission() async {
    final status = await Permission.microphone.status;
    if (status.isDenied) {
      final result = await Permission.microphone.request();
      return result.isGranted;
    }
    return status.isGranted;
  }

  // Start audio recording and streaming
  Future<void> _startAudioRecording() async {
    try {
      // Configure recording
      const config = RecordConfig(
        encoder: AudioEncoder.pcm16bits,
        sampleRate: 16000,
        numChannels: 1,
        autoGain: true,
        echoCancel: true,
        noiseSuppress: true,
      );

      // Get temporary directory for audio chunks
      final tempDir = await getTemporaryDirectory();
      final audioPath = '${tempDir.path}/streaming_audio.wav';

      // Start recording
      await _recorder.start(config, path: audioPath);

      // Send audio data periodically
      _recordingTimer = Timer.periodic(Duration(milliseconds: 100), (timer) async {
        if (!_isStreaming || _channel == null) {
          timer.cancel();
          return;
        }

        try {
          // Read audio data
          if (await _recorder.isRecording()) {
            // Note: This is a simplified approach.
            // For real-time streaming, you'd need to capture raw audio data
            // and send it as binary data to the WebSocket

            // For demonstration, we'll send a keep-alive message
            // In a real implementation, you'd capture and send raw PCM audio data
            await _sendAudioData();
          }
        } catch (e) {
          print('Error sending audio data: $e');
        }
      });

    } catch (e) {
      print('Error starting audio recording: $e');
      throw e;
    }
  }

  // Send audio data to WebSocket
  Future<void> _sendAudioData() async {
    // Note: This is a placeholder implementation
    // In a real application, you would:
    // 1. Capture raw PCM audio data from the microphone
    // 2. Convert it to the required format (16kHz, 16-bit PCM)
    // 3. Send it as binary data to the WebSocket

    // For now, we'll send a heartbeat to keep the connection alive
    if (_channel != null) {
      // In real implementation, send binary audio data:
      // _channel!.sink.add(audioDataBytes);
    }
  }

  // Get active sessions
  static Future<Map<String, dynamic>> getActiveSessions() async {
    try {
      final headers = await AuthService.getAuthHeaders();
      final response = await http.get(
        Uri.parse('${ApiConfig.baseUrl}${ApiConfig.streamingActiveSessionsEndpoint}'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return {'success': true, 'data': data};
      } else {
        final error = jsonDecode(response.body);
        return {'success': false, 'error': error['detail'] ?? 'Failed to get active sessions'};
      }
    } catch (e) {
      return {'success': false, 'error': 'Network error: $e'};
    }
  }

  // Terminate session
  Future<Map<String, dynamic>> terminateSession() async {
    if (_sessionId == null) {
      return {'success': false, 'error': 'No active session'};
    }

    try {
      final token = await AuthService.getToken();
      final headers = await AuthService.getAuthHeaders();

      final response = await http.delete(
        Uri.parse('${ApiConfig.baseUrl}${ApiConfig.streamingSessionEndpoint}/$_sessionId?token=$token'),
        headers: headers,
      );

      await _stopStreaming();

      if (response.statusCode == 200) {
        return {'success': true, 'message': 'Session terminated'};
      } else {
        final error = jsonDecode(response.body);
        return {'success': false, 'error': error['detail'] ?? 'Failed to terminate session'};
      }
    } catch (e) {
      return {'success': false, 'error': 'Network error: $e'};
    }
  }

  // Dispose resources
  void dispose() {
    _stopStreaming();
    _recorder.dispose();
  }
}
```

## Complete Example Widget

```dart
// lib/widgets/transcription_widget.dart
import 'dart:io';
import 'dart:async';
import 'package:flutter/material.dart';
import '../services/upload_service.dart';
import '../services/transcription_service.dart';
import '../services/auth_service.dart';

class TranscriptionWidget extends StatefulWidget {
  @override
  _TranscriptionWidgetState createState() => _TranscriptionWidgetState();
}

class _TranscriptionWidgetState extends State<TranscriptionWidget> {
  File? _selectedFile;
  bool _isUploading = false;
  bool _isTranscribing = false;
  String? _uploadUrl;
  Map<String, dynamic>? _transcriptionData;
  String? _transcriptionText;
  Timer? _statusTimer;

  @override
  void dispose() {
    _statusTimer?.cancel();
    super.dispose();
  }

  Future<void> _pickFile() async {
    final file = await UploadService.pickAudioFile();
    setState(() {
      _selectedFile = file;
    });
  }

  Future<void> _uploadFile() async {
    if (_selectedFile == null) return;

    setState(() {
      _isUploading = true;
    });

    final result = await UploadService.uploadFile(_selectedFile!);

    setState(() {
      _isUploading = false;
    });

    if (result['success']) {
      setState(() {
        _uploadUrl = result['data']['upload_url'];
      });
      _showSnackBar('File uploaded successfully!', Colors.green);
    } else {
      _showSnackBar('Upload failed: ${result['error']}', Colors.red);
    }
  }

  Future<void> _submitTranscription() async {
    if (_uploadUrl == null || _selectedFile == null) return;

    setState(() {
      _isTranscribing = true;
    });

    final options = TranscriptionOptions(
      languageCode: 'en',
      punctuate: true,
      formatText: true,
      speakerLabels: true,
      sentimentAnalysis: false,
    );

    final result = await TranscriptionService.submitTranscription(
      audioUrl: _uploadUrl!,
      audioFilename: _selectedFile!.path.split('/').last,
      estimatedDurationMinutes: 1.0,
      options: options,
    );

    if (result['success']) {
      setState(() {
        _transcriptionData = result['data'];
      });
      _showSnackBar('Transcription submitted!', Colors.green);
      _startStatusPolling();
    } else {
      setState(() {
        _isTranscribing = false;
      });
      _showSnackBar('Submission failed: ${result['error']}', Colors.red);
    }
  }

  void _startStatusPolling() {
    _statusTimer = Timer.periodic(Duration(seconds: 3), (timer) async {
      if (_transcriptionData == null) {
        timer.cancel();
        return;
      }

      final result = await TranscriptionService.getTranscriptionStatus(
        _transcriptionData!['transcription_id']
      );

      if (result['success']) {
        final status = result['data']['status'];

        if (status == 'completed') {
          timer.cancel();
          await _getTranscriptionResult();
        } else if (status == 'error') {
          timer.cancel();
          setState(() {
            _isTranscribing = false;
          });
          _showSnackBar('Transcription failed', Colors.red);
        }
      }
    });
  }

  Future<void> _getTranscriptionResult() async {
    if (_transcriptionData == null) return;

    final result = await TranscriptionService.getTranscriptionResult(
      _transcriptionData!['transcription_id']
    );

    setState(() {
      _isTranscribing = false;
    });

    if (result['success']) {
      setState(() {
        _transcriptionText = result['data']['text'];
      });
      _showSnackBar('Transcription completed!', Colors.green);
    } else {
      _showSnackBar('Failed to get results: ${result['error']}', Colors.red);
    }
  }

  void _showSnackBar(String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Audio Transcription'),
        actions: [
          IconButton(
            icon: Icon(Icons.logout),
            onPressed: () async {
              await AuthService.logout();
              Navigator.pushReplacementNamed(context, '/login');
            },
          ),
        ],
      ),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // File Selection
            Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Text('Select Audio File', style: Theme.of(context).textTheme.headlineSmall),
                    SizedBox(height: 16),
                    if (_selectedFile != null)
                      Text('Selected: ${_selectedFile!.path.split('/').last}'),
                    SizedBox(height: 16),
                    ElevatedButton.icon(
                      onPressed: _pickFile,
                      icon: Icon(Icons.file_upload),
                      label: Text('Choose File'),
                    ),
                  ],
                ),
              ),
            ),

            SizedBox(height: 16),

            // Upload Section
            Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Text('Upload Audio', style: Theme.of(context).textTheme.headlineSmall),
                    SizedBox(height: 16),
                    ElevatedButton.icon(
                      onPressed: _selectedFile != null && !_isUploading ? _uploadFile : null,
                      icon: _isUploading
                          ? SizedBox(width: 16, height: 16, child: CircularProgressIndicator(strokeWidth: 2))
                          : Icon(Icons.cloud_upload),
                      label: Text(_isUploading ? 'Uploading...' : 'Upload File'),
                    ),
                    if (_uploadUrl != null)
                      Padding(
                        padding: EdgeInsets.only(top: 8),
                        child: Text('✅ File uploaded successfully', style: TextStyle(color: Colors.green)),
                      ),
                  ],
                ),
              ),
            ),

            SizedBox(height: 16),

            // Transcription Section
            Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Text('Transcription', style: Theme.of(context).textTheme.headlineSmall),
                    SizedBox(height: 16),
                    ElevatedButton.icon(
                      onPressed: _uploadUrl != null && !_isTranscribing ? _submitTranscription : null,
                      icon: _isTranscribing
                          ? SizedBox(width: 16, height: 16, child: CircularProgressIndicator(strokeWidth: 2))
                          : Icon(Icons.transcribe),
                      label: Text(_isTranscribing ? 'Transcribing...' : 'Start Transcription'),
                    ),
                  ],
                ),
              ),
            ),

            SizedBox(height: 16),

            // Results Section
            if (_transcriptionText != null)
              Expanded(
                child: Card(
                  child: Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('Results', style: Theme.of(context).textTheme.headlineSmall),
                        SizedBox(height: 16),
                        Expanded(
                          child: SingleChildScrollView(
                            child: Container(
                              width: double.infinity,
                              padding: EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Colors.grey[100],
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                _transcriptionText!,
                                style: TextStyle(fontSize: 16),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
```

## Streaming Example

### Real-Time Streaming Widget

```dart
// lib/widgets/streaming_widget.dart
import 'dart:async';
import 'package:flutter/material.dart';
import '../services/streaming_service.dart';
import '../models/streaming_options.dart';

class StreamingTranscriptionWidget extends StatefulWidget {
  @override
  _StreamingTranscriptionWidgetState createState() => _StreamingTranscriptionWidgetState();
}

class _StreamingTranscriptionWidgetState extends State<StreamingTranscriptionWidget> {
  final StreamingTranscriptionService _streamingService = StreamingTranscriptionService();

  bool _isCreatingSession = false;
  bool _isStreaming = false;
  String _finalTranscript = '';
  String _interimTranscript = '';
  List<String> _transcriptHistory = [];
  StreamSubscription? _transcriptionSubscription;

  @override
  void dispose() {
    _transcriptionSubscription?.cancel();
    _streamingService.dispose();
    super.dispose();
  }

  Future<void> _createSession() async {
    setState(() {
      _isCreatingSession = true;
    });

    final options = StreamingOptions(
      sampleRate: 16000,
      encoding: 'pcm_s16le',
      channels: 1,
      interimResults: true,
      punctuate: true,
      formatText: true,
      speakerLabels: false,
      languageCode: 'en',
    );

    final result = await _streamingService.createSession(options: options);

    setState(() {
      _isCreatingSession = false;
    });

    if (result['success']) {
      _showSnackBar('Session created successfully!', Colors.green);
    } else {
      _showSnackBar('Session creation failed: ${result['error']}', Colors.red);
    }
  }

  Future<void> _startStreaming() async {
    if (_streamingService.sessionId == null) {
      _showSnackBar('Please create a session first', Colors.orange);
      return;
    }

    final result = await _streamingService.startStreaming();

    if (result['success']) {
      setState(() {
        _isStreaming = true;
      });

      // Listen to transcription stream
      _transcriptionSubscription = _streamingService.transcriptionStream?.listen(
        (data) {
          _handleTranscriptionMessage(data);
        },
        onError: (error) {
          print('Transcription stream error: $error');
          _showSnackBar('Streaming error: $error', Colors.red);
        },
      );

      _showSnackBar('Streaming started!', Colors.green);
    } else {
      _showSnackBar('Failed to start streaming: ${result['error']}', Colors.red);
    }
  }

  void _handleTranscriptionMessage(Map<String, dynamic> data) {
    setState(() {
      switch (data['type']) {
        case 'partial_transcript':
          _interimTranscript = data['text'] ?? '';
          break;

        case 'final_transcript':
          final finalText = data['text'] ?? '';
          if (finalText.isNotEmpty) {
            _finalTranscript += finalText + ' ';
            _transcriptHistory.add(finalText);
            _interimTranscript = '';
          }
          break;

        case 'session_begins':
          _showSnackBar('Session started', Colors.blue);
          break;

        case 'session_terminated':
          _showSnackBar('Session terminated', Colors.orange);
          _stopStreaming();
          break;

        case 'error':
          _showSnackBar('Error: ${data['error']}', Colors.red);
          break;

        default:
          print('Unknown message type: ${data['type']}');
      }
    });
  }

  Future<void> _stopStreaming() async {
    await _streamingService.stopStreaming();
    _transcriptionSubscription?.cancel();

    setState(() {
      _isStreaming = false;
      _interimTranscript = '';
    });

    _showSnackBar('Streaming stopped', Colors.orange);
  }

  Future<void> _pauseStreaming() async {
    await _streamingService.pauseStreaming();
    _showSnackBar('Streaming paused', Colors.orange);
  }

  Future<void> _resumeStreaming() async {
    await _streamingService.resumeStreaming();
    _showSnackBar('Streaming resumed', Colors.green);
  }

  Future<void> _terminateSession() async {
    final result = await _streamingService.terminateSession();

    if (result['success']) {
      setState(() {
        _finalTranscript = '';
        _interimTranscript = '';
        _transcriptHistory.clear();
        _isStreaming = false;
      });
      _showSnackBar('Session terminated', Colors.orange);
    } else {
      _showSnackBar('Failed to terminate session: ${result['error']}', Colors.red);
    }
  }

  void _clearTranscript() {
    setState(() {
      _finalTranscript = '';
      _interimTranscript = '';
      _transcriptHistory.clear();
    });
  }

  void _showSnackBar(String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        duration: Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Real-Time Transcription'),
        actions: [
          IconButton(
            icon: Icon(Icons.clear),
            onPressed: _clearTranscript,
            tooltip: 'Clear transcript',
          ),
        ],
      ),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Session Management
            Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Text('Session Management',
                         style: Theme.of(context).textTheme.headlineSmall),
                    SizedBox(height: 16),

                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _isCreatingSession ? null : _createSession,
                            icon: _isCreatingSession
                                ? SizedBox(width: 16, height: 16,
                                           child: CircularProgressIndicator(strokeWidth: 2))
                                : Icon(Icons.add_circle),
                            label: Text(_isCreatingSession ? 'Creating...' : 'Create Session'),
                          ),
                        ),
                        SizedBox(width: 8),
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _streamingService.sessionId != null ? _terminateSession : null,
                            icon: Icon(Icons.delete),
                            label: Text('Terminate'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.red,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),

                    if (_streamingService.sessionId != null)
                      Padding(
                        padding: EdgeInsets.only(top: 8),
                        child: Text('Session ID: ${_streamingService.sessionId}',
                                   style: TextStyle(fontSize: 12, color: Colors.grey)),
                      ),
                  ],
                ),
              ),
            ),

            SizedBox(height: 16),

            // Streaming Controls
            Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Text('Streaming Controls',
                         style: Theme.of(context).textTheme.headlineSmall),
                    SizedBox(height: 16),

                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _streamingService.sessionId != null && !_isStreaming
                                ? _startStreaming : null,
                            icon: Icon(Icons.mic),
                            label: Text('Start'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                        SizedBox(width: 8),
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _isStreaming ? _pauseStreaming : null,
                            icon: Icon(Icons.pause),
                            label: Text('Pause'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.orange,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                        SizedBox(width: 8),
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _isStreaming ? _resumeStreaming : null,
                            icon: Icon(Icons.play_arrow),
                            label: Text('Resume'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                        SizedBox(width: 8),
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _isStreaming ? _stopStreaming : null,
                            icon: Icon(Icons.stop),
                            label: Text('Stop'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.red,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),

                    SizedBox(height: 8),

                    // Status indicator
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: _isStreaming ? Colors.green : Colors.grey,
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            _isStreaming ? Icons.mic : Icons.mic_off,
                            color: Colors.white,
                            size: 16,
                          ),
                          SizedBox(width: 4),
                          Text(
                            _isStreaming ? 'LIVE' : 'STOPPED',
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            SizedBox(height: 16),

            // Transcription Display
            Expanded(
              child: Card(
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Real-Time Transcription',
                           style: Theme.of(context).textTheme.headlineSmall),
                      Divider(),

                      Expanded(
                        child: SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Final transcript
                              if (_finalTranscript.isNotEmpty) ...[
                                Text('Final Transcript:',
                                     style: TextStyle(fontWeight: FontWeight.bold)),
                                SizedBox(height: 8),
                                Container(
                                  width: double.infinity,
                                  padding: EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color: Colors.grey[100],
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Text(
                                    _finalTranscript,
                                    style: TextStyle(fontSize: 16),
                                  ),
                                ),
                                SizedBox(height: 16),
                              ],

                              // Interim transcript
                              if (_interimTranscript.isNotEmpty) ...[
                                Text('Interim Transcript:',
                                     style: TextStyle(
                                       fontWeight: FontWeight.bold,
                                       color: Colors.orange,
                                     )),
                                SizedBox(height: 8),
                                Container(
                                  width: double.infinity,
                                  padding: EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color: Colors.orange[50],
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(color: Colors.orange[200]!),
                                  ),
                                  child: Text(
                                    _interimTranscript,
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontStyle: FontStyle.italic,
                                      color: Colors.orange[800],
                                    ),
                                  ),
                                ),
                                SizedBox(height: 16),
                              ],

                              // Transcript history
                              if (_transcriptHistory.isNotEmpty) ...[
                                Text('Transcript History:',
                                     style: TextStyle(fontWeight: FontWeight.bold)),
                                SizedBox(height: 8),
                                ...List.generate(_transcriptHistory.length, (index) {
                                  return Padding(
                                    padding: EdgeInsets.only(bottom: 8),
                                    child: Container(
                                      width: double.infinity,
                                      padding: EdgeInsets.all(12),
                                      decoration: BoxDecoration(
                                        color: Colors.blue[50],
                                        borderRadius: BorderRadius.circular(8),
                                        border: Border.all(color: Colors.blue[200]!),
                                      ),
                                      child: Row(
                                        children: [
                                          Text(
                                            '${index + 1}. ',
                                            style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                              color: Colors.blue[800],
                                            ),
                                          ),
                                          Expanded(
                                            child: Text(
                                              _transcriptHistory[index],
                                              style: TextStyle(fontSize: 14),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  );
                                }),
                              ],

                              // Empty state
                              if (_finalTranscript.isEmpty &&
                                  _interimTranscript.isEmpty &&
                                  _transcriptHistory.isEmpty)
                                Center(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Icons.mic_none,
                                        size: 64,
                                        color: Colors.grey[400],
                                      ),
                                      SizedBox(height: 16),
                                      Text(
                                        'Start streaming to see transcription results',
                                        style: TextStyle(
                                          color: Colors.grey[600],
                                          fontSize: 16,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
```

## API Reference

### Response Formats

#### Authentication Response

```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "user": {
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe"
  }
}
```

#### Upload Response

```json
{
  "upload_url": "https://api.assemblyai.com/v2/upload/...",
  "filename": "audio.wav",
  "size_bytes": "1048576",
  "format": ".wav"
}
```

#### Transcription Submit Response

```json
{
  "transcription_id": 123,
  "assemblyai_id": "abc123-def456-ghi789",
  "status": "queued",
  "estimated_cost_credits": 10,
  "message": "Transcription job submitted successfully"
}
```

#### Streaming Session Create Response

```json
{
  "session_id": "session_abc123",
  "status": "created",
  "estimated_cost_credits": 1,
  "expires_at": "2024-01-01T12:00:00Z",
  "websocket_url": "ws://localhost:8000/api/v1/streaming/ws/session_abc123",
  "options": {
    "sample_rate": 16000,
    "encoding": "pcm_s16le",
    "channels": 1,
    "language_code": "en"
  }
}
```

#### Streaming WebSocket Messages

**Session Begins Message:**

```json
{
  "type": "session_begins",
  "session_id": "session_abc123",
  "message": "Session started"
}
```

**Partial Transcript Message:**

```json
{
  "type": "partial_transcript",
  "text": "Hello world",
  "confidence": 0.95,
  "audio_start": 1000,
  "audio_end": 2000
}
```

**Final Transcript Message:**

```json
{
  "type": "final_transcript",
  "text": "Hello world!",
  "confidence": 0.98,
  "audio_start": 1000,
  "audio_end": 2500,
  "words": [
    {
      "text": "Hello",
      "start": 1000,
      "end": 1500,
      "confidence": 0.99
    },
    {
      "text": "world!",
      "start": 1600,
      "end": 2500,
      "confidence": 0.97
    }
  ]
}
```

**Error Message:**

```json
{
  "type": "error",
  "error": "Authentication failed",
  "code": 4001
}
```

**Session Terminated Message:**

```json
{
  "type": "session_terminated",
  "session_id": "session_abc123",
  "reason": "completed",
  "total_audio_duration": 120.5,
  "total_cost_credits": 3
}
```

### Status Values

#### File Transcription Status

- `queued` - Transcription is queued for processing
- `processing` - Transcription is currently being processed
- `completed` - Transcription completed successfully
- `error` - Transcription failed

#### Streaming Session Status

- `created` - Session created and ready for streaming
- `active` - Session is actively streaming
- `paused` - Session is temporarily paused
- `stopped` - Session is stopped but not terminated
- `terminated` - Session has been permanently terminated

#### WebSocket Error Codes

- `4001` - Authentication failed
- `4002` - Authorization failed (insufficient credits)
- `4003` - Feature not available (requires paid plan)
- `4004` - Session not found
- `4005` - Session expired
- `4006` - Invalid audio format
- `4007` - Rate limit exceeded

### Supported Audio Formats

#### File Upload Formats

- MP3, WAV, FLAC, M4A, AAC, OGG, WMA, AMR, 3GP, MP4, MOV, AVI
- Maximum file size: 100MB

#### Streaming Audio Formats

- **PCM 16-bit**: Recommended for best quality
- **Sample Rates**: 8kHz, 16kHz, 22kHz, 44.1kHz, 48kHz (16kHz recommended)
- **Channels**: Mono (1 channel) or Stereo (2 channels)
- **Encoding**: PCM, μ-law, A-law
- **Real-time**: Audio must be streamed at real-time speed or slower

## Best Practices

### General API Usage

1. **Error Handling**: Always check the `success` field in API responses
2. **Token Management**: Store tokens securely and handle expiration
3. **File Validation**: Validate file types and sizes before upload
4. **Status Polling**: Use reasonable intervals (3-5 seconds) when polling for status
5. **Memory Management**: Dispose of timers and streams properly
6. **User Feedback**: Provide clear progress indicators and error messages

### Real-Time Streaming

7. **Session Management**: Always terminate sessions when done to avoid unnecessary charges
8. **Audio Quality**: Use 16kHz, 16-bit PCM mono for optimal results
9. **Connection Handling**: Implement proper reconnection logic for WebSocket failures
10. **Permission Handling**: Check microphone permissions before starting streaming
11. **Resource Cleanup**: Dispose of audio recorders and WebSocket connections properly
12. **Credit Monitoring**: Monitor credit usage to avoid service interruption
13. **Latency Optimization**: Send audio data in small chunks (100ms recommended)
14. **Error Recovery**: Handle WebSocket errors gracefully with user feedback

### Performance Optimization

15. **Concurrent Sessions**: Limit concurrent streaming sessions (max 5 per user)
16. **Audio Buffering**: Use appropriate buffer sizes to balance latency and stability
17. **Network Awareness**: Handle network connectivity changes in streaming mode
18. **State Management**: Maintain proper session state across app lifecycle events

## Troubleshooting

### Common File Upload Issues

1. **401 Unauthorized**: Token expired or invalid - call login again
2. **413 Request Entity Too Large**: File too large - compress or use smaller file
3. **400 Bad Request**: Invalid file format or missing parameters
4. **500 Internal Server Error**: Server issue - retry after a few minutes

### Common Streaming Issues

5. **WebSocket Connection Failed**: Check network connectivity and authentication token
6. **4003 Feature Not Available**: Streaming requires a paid AssemblyAI account
7. **4002 Insufficient Credits**: Top up account credits to continue streaming
8. **Audio Not Detected**: Check microphone permissions and audio recording setup
9. **Session Expired**: Sessions expire after 2 hours - create a new session
10. **Poor Audio Quality**: Ensure proper audio format (16kHz PCM recommended)
11. **High Latency**: Reduce audio chunk size or check network connection
12. **Memory Issues**: Dispose of resources properly and limit concurrent sessions

### Debug Tips

- Enable verbose logging for WebSocket connections
- Monitor audio data flow in development builds
- Test with different audio sources and formats
- Use network monitoring tools to diagnose connection issues
- Check server logs for detailed error information

---

For additional support, refer to the FastAPI server logs or contact the development team.
