import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/transcription_history.dart';

class HistoryCacheService {
  static const String _cacheKey = 'transcription_history_cache';
  static const String _cacheTimestampKey =
      'transcription_history_cache_timestamp';
  static const String _totalCountKey = 'transcription_history_total_count';

  // Cache expiry time (24 hours)
  static const Duration cacheExpiry = Duration(hours: 24);

  /// Save transcription history to cache
  static Future<void> saveToCache({
    required List<TranscriptionHistoryItem> items,
    required int totalCount,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Convert items to JSON
      final jsonItems = items.map((item) => item.toJson()).toList();
      final cacheData = {
        'items': jsonItems,
        'cached_at': DateTime.now().toIso8601String(),
        'total_count': totalCount,
      };

      await prefs.setString(_cache<PERSON>ey, jsonEncode(cacheData));
      await prefs.setString(
        _cacheTimestampKey,
        DateTime.now().toIso8601String(),
      );
      await prefs.setInt(_totalCountKey, totalCount);

      print('💾 Cached ${items.length} history items (total: $totalCount)');
    } catch (e) {
      print('❌ Failed to save cache: $e');
    }
  }

  /// Load transcription history from cache
  static Future<HistoryCacheResult?> loadFromCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheData = prefs.getString(_cacheKey);
      final timestampStr = prefs.getString(_cacheTimestampKey);

      if (cacheData == null || timestampStr == null) {
        print('📭 No cache found');
        return null;
      }

      final cacheTimestamp = DateTime.parse(timestampStr);
      final age = DateTime.now().difference(cacheTimestamp);

      final jsonData = jsonDecode(cacheData);
      final items = (jsonData['items'] as List)
          .map((item) => TranscriptionHistoryItem.fromJson(item))
          .toList();
      final totalCount = jsonData['total_count'] ?? items.length;

      print(
        '📥 Loaded ${items.length} items from cache (age: ${age.inMinutes}min, total: $totalCount)',
      );

      return HistoryCacheResult(
        items: items,
        totalCount: totalCount,
        cacheAge: age,
        isExpired: age > cacheExpiry,
      );
    } catch (e) {
      print('❌ Failed to load cache: $e');
      return null;
    }
  }

  /// Add a new transcription to the beginning of the cache
  static Future<void> prependToCache(TranscriptionHistoryItem newItem) async {
    try {
      final currentCache = await loadFromCache();
      if (currentCache == null) {
        // No cache exists, create new with single item
        await saveToCache(items: [newItem], totalCount: 1);
        return;
      }

      // Check if item already exists (avoid duplicates)
      final existingIndex = currentCache.items.indexWhere(
        (item) => item.transcriptionId == newItem.transcriptionId,
      );

      List<TranscriptionHistoryItem> updatedItems;
      int newTotalCount;

      if (existingIndex >= 0) {
        // Update existing item
        updatedItems = List.from(currentCache.items);
        updatedItems[existingIndex] = newItem;
        newTotalCount = currentCache.totalCount;
        print('🔄 Updated existing item in cache: ${newItem.transcriptionId}');
      } else {
        // Add new item to beginning
        updatedItems = [newItem, ...currentCache.items];
        newTotalCount = currentCache.totalCount + 1;
        print('➕ Added new item to cache: ${newItem.transcriptionId}');
      }

      await saveToCache(items: updatedItems, totalCount: newTotalCount);
    } catch (e) {
      print('❌ Failed to prepend to cache: $e');
    }
  }

  /// Update an existing item in cache
  static Future<void> updateInCache(
    TranscriptionHistoryItem updatedItem,
  ) async {
    try {
      final currentCache = await loadFromCache();
      if (currentCache == null) return;

      final itemIndex = currentCache.items.indexWhere(
        (item) => item.transcriptionId == updatedItem.transcriptionId,
      );

      if (itemIndex >= 0) {
        final updatedItems = List<TranscriptionHistoryItem>.from(
          currentCache.items,
        );
        updatedItems[itemIndex] = updatedItem;

        await saveToCache(
          items: updatedItems,
          totalCount: currentCache.totalCount,
        );
        print('🔄 Updated cache item: ${updatedItem.transcriptionId}');
      }
    } catch (e) {
      print('❌ Failed to update cache: $e');
    }
  }

  /// Check if cache exists and is valid
  static Future<bool> hasFreshCache() async {
    final cache = await loadFromCache();
    return cache != null && !cache.isExpired && cache.items.isNotEmpty;
  }

  /// Clear all cached data
  static Future<void> clearCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_cacheKey);
      await prefs.remove(_cacheTimestampKey);
      await prefs.remove(_totalCountKey);
      print('🗑️ Cache cleared');
    } catch (e) {
      print('❌ Failed to clear cache: $e');
    }
  }

  /// Get cache info for debugging
  static Future<String> getCacheInfo() async {
    final cache = await loadFromCache();
    if (cache == null) {
      return 'No cache';
    }

    return 'Cache: ${cache.items.length} items, '
        'age: ${cache.cacheAge.inMinutes}min, '
        'expired: ${cache.isExpired}';
  }
}

/// Result object for cache operations
class HistoryCacheResult {
  final List<TranscriptionHistoryItem> items;
  final int totalCount;
  final Duration cacheAge;
  final bool isExpired;

  HistoryCacheResult({
    required this.items,
    required this.totalCount,
    required this.cacheAge,
    required this.isExpired,
  });

  bool get isFresh => !isExpired && items.isNotEmpty;
}
