import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:http/http.dart' as http;
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:web_socket_channel/io.dart';

class AssemblyAIService {
  static const String _baseUrl = 'https://api.assemblyai.com';
  static const String _streamingUrl = 'wss://streaming.assemblyai.com/v3/ws';
  static const String _apiKey = '41418bef519a48acbf0b2312097895c9';

  static const Map<String, String> _headers = {
    'authorization': _apiKey,
    'content-type': 'application/json',
  };

  // Streaming connection and configuration
  static WebSocketChannel? _streamingChannel;
  static bool _isStreaming = false;

  static const Map<String, dynamic> _streamingParams = {
    'sample_rate': 16000,
    'format_turns': true,
  };

  static bool get isStreaming => _isStreaming;

  // Upload audio file to QuantXHub
  static Future<String> uploadAudio(String filePath) async {
    final uri = Uri.parse('$_baseUrl/v2/upload');
    final file = File(filePath);

    // Check if file exists and has content
    if (!await file.exists()) {
      throw Exception('Audio file does not exist: $filePath');
    }

    final fileSize = await file.length();
    if (fileSize == 0) {
      throw Exception('Audio file is empty: $filePath');
    }

    print('Uploading audio file: $filePath (${fileSize} bytes)');

    // Read file as bytes and upload directly (like AssemblyAI Python SDK does)
    final fileBytes = await file.readAsBytes();

    final response = await http.post(
      uri,
      headers: {
        'authorization': _apiKey,
        'content-type': 'application/octet-stream', // Upload raw bytes
      },
      body: fileBytes,
    );

    print('Upload response: ${response.statusCode} - ${response.body}');

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return data['upload_url'];
    } else {
      throw Exception('Failed to upload audio: ${response.body}');
    }
  }

  // Start transcription
  static Future<String> startTranscription(String audioUrl) async {
    final uri = Uri.parse('$_baseUrl/v2/transcript');

    final data = {
      'audio_url': audioUrl,
      'speech_model': 'nano', // Fastest model for real-time speed
    };

    final response = await http.post(
      uri,
      headers: _headers,
      body: json.encode(data),
    );

    if (response.statusCode == 200) {
      final responseData = json.decode(response.body);
      return responseData['id'];
    } else {
      throw Exception('Failed to start transcription: ${response.body}');
    }
  }

  // Get transcription result
  static Future<Map<String, dynamic>> getTranscription(
    String transcriptId,
  ) async {
    final uri = Uri.parse('$_baseUrl/v2/transcript/$transcriptId');

    final response = await http.get(uri, headers: _headers);

    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception('Failed to get transcription: ${response.body}');
    }
  }

  // Poll for transcription completion
  static Future<String> pollForTranscription(String transcriptId) async {
    while (true) {
      final result = await getTranscription(transcriptId);
      final status = result['status'];

      if (status == 'completed') {
        return result['text'] ?? '';
      } else if (status == 'error') {
        throw Exception('Transcription failed: ${result['error']}');
      }

      // Wait only 500ms for lightning-fast updates
      await Future.delayed(const Duration(milliseconds: 500));
    }
  }

  // Complete transcription process from file
  static Future<String> transcribeAudioFile(String filePath) async {
    try {
      // Upload audio file
      final audioUrl = await uploadAudio(filePath);

      // Start transcription
      final transcriptId = await startTranscription(audioUrl);

      // Poll for completion and return text
      return await pollForTranscription(transcriptId);
    } catch (e) {
      throw Exception('Transcription process failed: $e');
    }
  }

  // STREAMING FUNCTIONALITY

  // Start streaming session
  static Future<WebSocketChannel> startStreaming({
    required Function(String) onTranscript,
    required Function(String) onFinalTranscript,
    required Function(String) onError,
  }) async {
    try {
      // Build WebSocket URL with parameters
      final uri = Uri.parse(_streamingUrl).replace(
        queryParameters: _streamingParams.map(
          (key, value) => MapEntry(key, value.toString()),
        ),
      );

      print('Connecting to streaming: $uri');

      // Create WebSocket connection with Authorization header
      _streamingChannel = IOWebSocketChannel.connect(
        uri,
        headers: {'Authorization': _apiKey},
      );

      _isStreaming = true;

      // Listen to messages
      _streamingChannel!.stream.listen(
        (message) {
          try {
            final data = json.decode(message);
            final msgType = data['type'];

            print('Received message type: $msgType');

            if (msgType == 'Begin') {
              final sessionId = data['id'];
              print('Streaming session started: $sessionId');
            } else if (msgType == 'Turn') {
              final transcript = data['transcript'] ?? '';
              final isFormatted = data['turn_is_formatted'] == true;

              if (isFormatted) {
                onFinalTranscript(transcript);
              } else {
                onTranscript(transcript);
              }
            } else if (msgType == 'Termination') {
              print('Streaming session terminated');
              _isStreaming = false;
            }
          } catch (e) {
            print('Error processing streaming message: $e');
            onError('Error processing message: $e');
          }
        },
        onError: (error) {
          print('Streaming WebSocket error: $error');
          _isStreaming = false;
          onError('Streaming error: $error');
        },
        onDone: () {
          print('Streaming WebSocket closed');
          _isStreaming = false;
        },
      );

      return _streamingChannel!;
    } catch (e) {
      _isStreaming = false;
      throw Exception('Failed to start streaming: $e');
    }
  }

  // Send audio data to streaming service
  static void sendAudioData(Uint8List audioData) {
    if (_isStreaming && _streamingChannel != null) {
      try {
        _streamingChannel!.sink.add(audioData);
      } catch (e) {
        print('Error sending audio data: $e');
      }
    }
  }

  // Stop streaming
  static Future<void> stopStreaming() async {
    if (_isStreaming && _streamingChannel != null) {
      try {
        // Send termination message
        _streamingChannel!.sink.add(json.encode({'type': 'Terminate'}));

        // Close connection
        await _streamingChannel!.sink.close();
        _streamingChannel = null;
        _isStreaming = false;

        print('Streaming stopped');
      } catch (e) {
        print('Error stopping streaming: $e');
        _streamingChannel = null;
        _isStreaming = false;
      }
    }
  }
}
