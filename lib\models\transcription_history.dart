import '../utils/timezone_utils.dart';

class TranscriptionHistoryItem {
  final String transcriptionId;
  final String status;
  final DateTime createdAt;
  final DateTime? completedAt;
  final int? audioDuration; // in seconds
  final int creditsUsed;
  final String? filename;
  final String? text; // The actual transcription text
  final TranscriptionType type;
  final double? confidence;
  final int? wordCount;

  TranscriptionHistoryItem({
    required this.transcriptionId,
    required this.status,
    required this.createdAt,
    this.completedAt,
    this.audioDuration,
    required this.creditsUsed,
    this.filename,
    this.text,
    required this.type,
    this.confidence,
    this.wordCount,
  });

  factory TranscriptionHistoryItem.fromJson(Map<String, dynamic> json) {
    print('🔍 Parsing history item: $json');

    // Extract ID - convert to string since API returns int
    String transcriptionId = (json['id'] ?? json['transcription_id'] ?? '')
        .toString();

    // Extract text - API uses 'transcript_text' field
    String? text =
        json['transcript_text'] ??
        json['text'] ??
        json['transcription'] ??
        json['result'];

    // Debug: Log the text we received to identify truncation
    if (text != null) {
      final textLength = text.length;
      final clientWordCount = text.trim().split(RegExp(r'\s+')).length;
      print(
        '🔍 Raw text received: length=$textLength chars, words=$clientWordCount',
      );
      print(
        '🔍 Text preview: "${text.substring(0, text.length > 100 ? 100 : text.length)}..."',
      );

      // Check if text appears truncated (ends abruptly without punctuation)
      if (textLength > 50 &&
          !text.trim().endsWith('.') &&
          !text.trim().endsWith('!') &&
          !text.trim().endsWith('?')) {
        print('⚠️ WARNING: Text may be truncated (no ending punctuation)');
      }
    } else {
      print('⚠️ No text found in any expected field');
    }

    // Extract filename - API uses 'audio_filename'
    String? filename = json['audio_filename'] ?? json['filename'];

    // Extract audio duration - try multiple field names and log what we find
    int? audioDuration;

    // Try different possible field names for audio duration
    final durationFields = [
      'audio_duration', // Standard field name
      'duration', // Alternative field name
      'audio_length', // Another alternative
      'recording_duration', // For recordings
      'file_duration', // For file uploads
      'length_seconds', // Alternative format
      'duration_seconds', // Alternative format
    ];

    for (String field in durationFields) {
      if (json[field] != null) {
        try {
          audioDuration = json[field] is int
              ? json[field]
              : int.parse(json[field].toString());
          print(
            '✅ Found audio duration in field "$field": $audioDuration seconds',
          );
          break;
        } catch (e) {
          print(
            '❌ Could not parse duration from field "$field": ${json[field]}',
          );
        }
      }
    }

    if (audioDuration == null) {
      print(
        '⚠️ No audio duration found in any field. Available fields: ${json.keys.toList()}',
      );
    }

    // Extract word count - prioritize server-provided count over client calculation
    int? wordCount;

    // Try to get word count from server first
    final wordCountFields = [
      'word_count',
      'words_count',
      'total_words',
      'num_words',
    ];

    for (String field in wordCountFields) {
      if (json[field] != null) {
        try {
          wordCount = json[field] is int
              ? json[field]
              : int.tryParse(json[field].toString());
          if (wordCount != null && wordCount > 0) {
            print(
              '✅ Found word count from server in field "$field": $wordCount words',
            );
            break;
          }
        } catch (e) {
          print(
            '❌ Could not parse word count from field "$field": ${json[field]}',
          );
        }
      }
    }

    // Always calculate client-side word count for verification and fallback
    int? clientWordCount;
    if (text != null && text.isNotEmpty) {
      clientWordCount = text.trim().split(RegExp(r'\s+')).length;

      print(
        '🔢 Client word count calculation: $clientWordCount words from ${text.length} characters',
      );

      // If server provided word count, verify it's reasonable
      if (wordCount != null) {
        print(
          '🔢 Server word count: $wordCount vs Client word count: $clientWordCount',
        );

        // If server word count is significantly different from client count, use client count
        final difference = (wordCount - clientWordCount).abs();
        final differencePercent = difference / clientWordCount * 100;

        if (difference > clientWordCount * 0.5) {
          // More than 50% difference
          print(
            '⚠️ Server word count ($wordCount) differs significantly from client count ($clientWordCount) by ${differencePercent.toStringAsFixed(1)}%. Using client count.',
          );
          wordCount = clientWordCount;
        } else {
          print(
            '✅ Server and client word counts are reasonably close (difference: $difference words)',
          );
        }
      } else {
        // Use client calculation if server didn't provide word count
        wordCount = clientWordCount;
        print('✅ Using client-side word count calculation: $wordCount words');
      }
    }

    // Final validation and logging
    if (wordCount == null || wordCount <= 0) {
      wordCount = 0;
      print('⚠️ No valid word count found, setting to 0');
    } else {
      print('✅ Final word count: $wordCount words');
    }

    print(
      '🔍 Extracted ID: "$transcriptionId", text: "${text?.substring(0, text.length > 50 ? 50 : text.length) ?? 'null'}...", filename: "$filename", duration: ${audioDuration ?? 'null'}s, words: ${wordCount ?? 'null'}',
    );

    return TranscriptionHistoryItem(
      transcriptionId: transcriptionId,
      status: json['status'] ?? '',
      createdAt: TimezoneUtils.parseToLocal(json['created_at']),
      completedAt: json['completed_at'] != null
          ? TimezoneUtils.parseToLocal(json['completed_at'])
          : null,
      audioDuration: audioDuration,
      creditsUsed: json['cost_credits'] ?? json['credits_used'] ?? 0,
      filename: filename,
      text: text,
      type: _getTranscriptionType(filename, json['type']),
      confidence:
          json['confidence_score']?.toDouble() ??
          json['confidence']?.toDouble(),
      wordCount: wordCount,
    );
  }

  static TranscriptionType _getTranscriptionType(
    String? filename,
    String? type,
  ) {
    // If type is explicitly provided, use it
    if (type != null) {
      return type.toLowerCase() == 'streaming'
          ? TranscriptionType.streaming
          : TranscriptionType.recording;
    }

    // Infer from filename patterns - much more accurate now
    if (filename != null) {
      final lowerFilename = filename.toLowerCase();

      // Check for streaming session indicators
      if (lowerFilename.contains('streaming_session_') ||
          lowerFilename.startsWith('streaming_session') ||
          lowerFilename.contains('stream')) {
        return TranscriptionType.streaming;
      }

      // Check for recorded file indicators
      if (lowerFilename.contains('.wav') ||
          lowerFilename.contains('.mp3') ||
          lowerFilename.contains('.m4a') ||
          lowerFilename.contains('voice_') ||
          lowerFilename.contains('recordings/') ||
          lowerFilename.contains('audio.wav')) {
        return TranscriptionType.recording;
      }
    }

    // Default to recording if we can't determine
    return TranscriptionType.recording;
  }

  String get durationFormatted {
    if (audioDuration == null) return 'Unknown';
    final minutes = audioDuration! ~/ 60;
    final seconds = audioDuration! % 60;
    return '${minutes}m ${seconds}s';
  }

  String get typeDisplay {
    return type == TranscriptionType.recording ? 'Recording' : 'Streaming';
  }

  String get statusDisplay {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'Completed';
      case 'processing':
        return 'Processing';
      case 'queued':
        return 'Queued';
      case 'error':
        return 'Error';
      default:
        return status;
    }
  }

  bool get isCompleted => status.toLowerCase() == 'completed';
  bool get isError => status.toLowerCase() == 'error';
  bool get isProcessing =>
      status.toLowerCase() == 'processing' || status.toLowerCase() == 'queued';

  /// Convert to JSON for caching
  Map<String, dynamic> toJson() {
    return {
      'id': transcriptionId,
      'transcription_id': transcriptionId,
      'status': status,
      'created_at': createdAt.toIso8601String(),
      'completed_at': completedAt?.toIso8601String(),
      'audio_duration': audioDuration,
      'cost_credits': creditsUsed,
      'credits_used': creditsUsed,
      'audio_filename': filename,
      'filename': filename,
      'transcript_text': text,
      'text': text,
      'type': type == TranscriptionType.streaming ? 'streaming' : 'recording',
      'confidence_score': confidence,
      'confidence': confidence,
      'word_count': wordCount,
    };
  }
}

enum TranscriptionType { recording, streaming }

class TranscriptionHistoryResponse {
  final List<TranscriptionHistoryItem> transcriptions;
  final int total;
  final int skip;
  final int limit;

  TranscriptionHistoryResponse({
    required this.transcriptions,
    required this.total,
    required this.skip,
    required this.limit,
  });

  factory TranscriptionHistoryResponse.fromJson(Map<String, dynamic> json) {
    return TranscriptionHistoryResponse(
      transcriptions:
          (json['transcriptions'] as List<dynamic>?)
              ?.map((item) => TranscriptionHistoryItem.fromJson(item))
              .toList() ??
          [],
      total: json['total'] ?? 0,
      skip:
          ((json['page'] ?? 1) - 1) *
          (json['per_page'] ?? 20), // Calculate skip from page info
      limit: json['per_page'] ?? json['limit'] ?? 20,
    );
  }
}
