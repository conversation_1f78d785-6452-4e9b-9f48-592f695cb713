import 'package:flutter/foundation.dart';
import 'dart:async';
import '../models/user.dart';
import '../services/auth_service.dart';

enum AuthState { initial, loading, authenticated, unauthenticated, error }

class AuthProvider extends ChangeNotifier {
  final AuthService _authService = AuthService.instance;

  AuthState _state = AuthState.initial;
  User? _user;
  String? _errorMessage;
  Timer? _creditRefreshTimer;
  DateTime? _lastCreditRefresh;

  AuthState get state => _state;
  User? get user => _user;
  String? get errorMessage => _errorMessage;
  bool get isAuthenticated => _state == AuthState.authenticated;
  bool get isLoading => _state == AuthState.loading;

  // Initialize authentication state
  Future<void> initializeAuth() async {
    _setState(AuthState.loading);

    try {
      if (await _authService.isLoggedIn()) {
        final user = await _authService.getCurrentUser();
        if (user != null) {
          _user = user;
          _setState(AuthState.authenticated);
          // Load initial credits but don't start periodic refresh
          await forceRefreshUserData(bypassThrottle: true);
          return;
        }
      }
      _setState(AuthState.unauthenticated);
    } catch (e) {
      _setError('Failed to initialize authentication');
    }
  }

  // Login user
  Future<bool> login(String email, String password) async {
    _setState(AuthState.loading);

    try {
      final loginRequest = LoginRequest(email: email, password: password);
      final authResponse = await _authService.login(loginRequest);
      _user = authResponse.user;
      _setState(AuthState.authenticated);
      // No periodic refresh - credits only update after transcription usage
      return true;
    } catch (e) {
      _setError(e.toString().replaceFirst('Exception: ', ''));
      return false;
    }
  }

  // Register user
  Future<bool> register({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    String? company,
    String? phone,
  }) async {
    _setState(AuthState.loading);

    try {
      final registerRequest = RegisterRequest(
        email: email,
        password: password,
        firstName: firstName,
        lastName: lastName,
        company: company,
        phone: phone,
      );

      final user = await _authService.register(registerRequest);

      // After successful registration, log the user in
      return await login(email, password);
    } catch (e) {
      _setError(e.toString().replaceFirst('Exception: ', ''));
      return false;
    }
  }

  // Logout user
  Future<void> logout() async {
    _setState(AuthState.loading);

    try {
      await _authService.logout();
      _stopPeriodicRefresh(); // Clean up any existing timer
      _user = null;
      _setState(AuthState.unauthenticated);
    } catch (e) {
      // Even if logout fails on server, clear local state
      _stopPeriodicRefresh();
      _user = null;
      _setState(AuthState.unauthenticated);
    }
  }

  // Immediate refresh for transcription completion (bypasses throttling)
  Future<void> refreshCreditsAfterTranscriptionComplete() async {
    if (_state != AuthState.authenticated) return;

    try {
      print('🔄 Immediate credit refresh after transcription completion...');
      _lastCreditRefresh = DateTime.now();

      final user = await _authService.getCurrentUser();
      if (user != null) {
        final oldCredits = _user?.creditsRemaining ?? 0;
        _user = user;
        final newCredits = _user?.creditsRemaining ?? 0;
        print(
          '💰 Credits immediately updated after transcription: $oldCredits → $newCredits',
        );
        notifyListeners();
      }
    } catch (e) {
      print('❌ Failed to immediately refresh credits after transcription: $e');
      // Reset throttle on error so we can retry sooner
      _lastCreditRefresh = null;
    }
  }

  // Refresh user data after transcription usage only
  Future<void> refreshUserDataAfterTranscription() async {
    if (_state != AuthState.authenticated) return;

    // Reduce throttle time from 30 to 10 seconds to allow more frequent updates
    // This fixes the issue where usage minutes show as 0 after first streaming
    if (_lastCreditRefresh != null &&
        DateTime.now().difference(_lastCreditRefresh!).inSeconds < 10) {
      print(
        '⏸️ Credit refresh throttled (last refresh was ${DateTime.now().difference(_lastCreditRefresh!).inSeconds} seconds ago)',
      );
      return;
    }

    try {
      print('🔄 Refreshing credits after transcription usage...');
      _lastCreditRefresh = DateTime.now();

      final user = await _authService.getCurrentUser();
      if (user != null) {
        final oldCredits = _user?.creditsRemaining ?? 0;
        _user = user;
        final newCredits = _user?.creditsRemaining ?? 0;
        print(
          '💰 Credits updated after transcription: $oldCredits → $newCredits',
        );
        notifyListeners();
      }
    } catch (e) {
      print('❌ Failed to refresh user data after transcription: $e');
      // Reset throttle on error so we can retry sooner
      _lastCreditRefresh = null;
    }
  }

  // Keep existing refreshUserData for backward compatibility but make it less aggressive
  Future<void> refreshUserData() async {
    // Redirect to the transcription-specific method with more conservative throttling
    await refreshUserDataAfterTranscription();
  }

  // Force refresh user data (bypasses throttling)
  Future<void> forceRefreshUserData({bool bypassThrottle = false}) async {
    if (_state != AuthState.authenticated) return;

    // Check throttling unless explicitly bypassed
    if (!bypassThrottle &&
        _lastCreditRefresh != null &&
        DateTime.now().difference(_lastCreditRefresh!).inMinutes < 2) {
      print('⏸️ Force refresh throttled, use refreshUserData() instead');
      return;
    }

    try {
      print('🔄 Force refreshing user data...');
      _lastCreditRefresh = DateTime.now();

      final user = await _authService.getCurrentUser();
      if (user != null) {
        final oldCredits = _user?.creditsRemaining ?? 0;
        _user = user;
        final newCredits = _user?.creditsRemaining ?? 0;
        print('💰 Credits force updated: $oldCredits → $newCredits');
        notifyListeners();
      }
    } catch (e) {
      print('❌ Failed to force refresh user data: $e');
      // Reset throttle on error so we can retry sooner
      _lastCreditRefresh = null;
    }
  }

  // Manual refresh triggered by user (bypasses throttling)
  Future<void> refreshCreditsNow() async {
    if (_state == AuthState.authenticated) {
      print('👆 Manual credit refresh requested by user');
      await forceRefreshUserData(bypassThrottle: true);
    }
  }

  // Remove periodic refresh - credits only update after transcription usage
  void _startPeriodicRefresh() {
    // Deprecated - no longer used
    // Credits are only refreshed after transcription usage
    print(
      'ℹ️ Periodic credit refresh disabled - credits update after transcription usage only',
    );
  }

  // Stop periodic credit refresh
  void _stopPeriodicRefresh() {
    _creditRefreshTimer?.cancel();
    _creditRefreshTimer = null;
  }

  // Override dispose to clean up timer
  @override
  void dispose() {
    _stopPeriodicRefresh();
    super.dispose();
  }

  // Clear error message
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Private helper methods
  void _setState(AuthState newState) {
    _state = newState;
    _errorMessage = null;
    notifyListeners();
  }

  void _setError(String message) {
    _state = AuthState.error;
    _errorMessage = message;
    notifyListeners();
  }
}
