import 'dart:io';
import 'dart:async';
import 'dart:typed_data';
import 'dart:math';
import 'package:record/record.dart';
import 'package:path_provider/path_provider.dart';
import 'package:file_picker/file_picker.dart';
import 'assembly_ai_service.dart';

class AudioService {
  static final AudioRecorder _recorder = AudioRecorder();
  static String? _recordingPath;
  static bool _isRecording = false;
  static bool _isStreamingMode = false;
  static StreamSubscription<Uint8List>? _streamSubscription;
  static Timer? _chunkTimer;
  static DateTime? _recordingStartTime; // Track recording start time
  static Duration? _lastRecordingDuration; // Track last recording duration

  // Audio level streaming for waveform visualization
  static final StreamController<double> _audioLevelController =
      StreamController<double>.broadcast();
  static double _currentAudioLevel = 0.0;

  static bool get isRecording => _isRecording;
  static bool get isStreamingMode => _isStreamingMode;
  static String? get recordingPath => _recordingPath;
  static Duration? get lastRecordingDuration =>
      _lastRecordingDuration; // Getter for last recording duration

  // Audio level stream for real-time waveform visualization
  static Stream<double> get audioLevelStream => _audioLevelController.stream;
  static double get currentAudioLevel => _currentAudioLevel;

  // Public method to emit audio levels from external sources (like StreamingTranscriptionService)
  static void emitAudioLevel(double level) {
    _currentAudioLevel = level;
    if (!_audioLevelController.isClosed) {
      _audioLevelController.add(_currentAudioLevel);
    }
  }

  // Calculate RMS amplitude from PCM 16-bit audio data (0.0 to 1.0) with voice-optimized filtering
  static double _calculateAudioLevel(Uint8List chunk) {
    if (chunk.length < 2) return 0.0;

    // Convert to samples first
    List<double> samples = [];
    for (int i = 0; i < chunk.length - 1; i += 2) {
      int sample = (chunk[i + 1] << 8) | chunk[i];
      if (sample > 32767) sample -= 65536; // Convert to signed
      samples.add(sample / 32768.0); // Normalize to -1.0 to 1.0
    }

    if (samples.isEmpty) return 0.0;

    // Apply high-pass filter to reduce low-frequency background noise (< 80Hz)
    List<double> filteredSamples = _applyHighPassFilter(samples, 16000, 80.0);

    // Apply voice-range emphasis (200Hz - 3000Hz where most speech energy is)
    filteredSamples = _applyVoiceEmphasis(filteredSamples, 16000);

    // Calculate RMS of filtered signal
    double sum = 0.0;
    for (double sample in filteredSamples) {
      sum += sample * sample;
    }

    double rms = sqrt(sum / filteredSamples.length);

    // Voice-optimized scaling with adaptive threshold
    // More aggressive scaling for voice frequencies, less for background noise
    double voiceLevel = (rms * 6.0).clamp(0.0, 1.0);

    // Apply adaptive threshold - higher sensitivity for voice-like patterns
    if (voiceLevel < 0.03) {
      return 0.0; // Filter out low-level background noise
    }

    // Enhance mid-to-high frequency content (where voice clarity is)
    voiceLevel = pow(
      voiceLevel,
      0.7,
    ).toDouble(); // Slight compression for better response

    return voiceLevel.clamp(0.0, 1.0);
  }

  // Simple high-pass filter to remove low-frequency background noise
  static List<double> _applyHighPassFilter(
    List<double> samples,
    double sampleRate,
    double cutoffFreq,
  ) {
    // Simple first-order high-pass filter
    double RC = 1.0 / (2.0 * pi * cutoffFreq);
    double dt = 1.0 / sampleRate;
    double alpha = RC / (RC + dt);

    List<double> filtered = List.filled(samples.length, 0.0);
    double prevInput = 0.0;
    double prevOutput = 0.0;

    for (int i = 0; i < samples.length; i++) {
      filtered[i] = alpha * (prevOutput + samples[i] - prevInput);
      prevInput = samples[i];
      prevOutput = filtered[i];
    }

    return filtered;
  }

  // Emphasize voice frequency range (200Hz - 3000Hz)
  static List<double> _applyVoiceEmphasis(
    List<double> samples,
    double sampleRate,
  ) {
    // Simple band-pass emphasis for voice frequencies
    // This is a simplified approach - in practice you'd use proper FFT filtering
    List<double> emphasized = List.from(samples);

    // Apply slight amplification to mid-frequencies and gentle roll-off for very high frequencies
    for (int i = 1; i < emphasized.length - 1; i++) {
      // Simple derivative filter to emphasize transitions (voice characteristics)
      double derivative = emphasized[i + 1] - emphasized[i - 1];
      emphasized[i] = emphasized[i] + (derivative * 0.3); // Moderate emphasis
    }

    return emphasized;
  }

  // Start recording audio (regular mode)
  static Future<void> startRecording() async {
    try {
      // Check permission first
      if (await hasPermission()) {
        final directory = await getTemporaryDirectory();
        final fileName = 'voice_${DateTime.now().millisecondsSinceEpoch}.wav';
        _recordingPath = '${directory.path}/$fileName';

        // Use WAV format with minimal config (let Windows handle defaults)
        const config = RecordConfig(encoder: AudioEncoder.wav);

        _recordingStartTime = DateTime.now(); // Track start time
        await _recorder.start(config, path: _recordingPath!);
        _isRecording = true;
        _isStreamingMode = false;

        // Start a timer to emit zero audio levels during regular recording
        // (since we don't have access to real-time data in file recording mode)
        _chunkTimer = Timer.periodic(const Duration(milliseconds: 50), (timer) {
          if (_isRecording && !_isStreamingMode) {
            // Emit a subtle pulsing animation for regular recording
            _currentAudioLevel =
                0.1 +
                (sin(DateTime.now().millisecondsSinceEpoch / 200.0) * 0.1);
            _audioLevelController.add(_currentAudioLevel);
          } else {
            timer.cancel();
          }
        });

        print('Recording started: $_recordingPath at ${_recordingStartTime}');
      } else {
        throw Exception('Microphone permission not granted');
      }
    } catch (e) {
      _isRecording = false;
      _recordingStartTime = null;
      throw Exception('Failed to start recording: $e');
    }
  }

  // Start streaming audio (real-time mode)
  static Future<void> startStreamingRecording({
    required Function(String) onTranscript,
    required Function(String) onFinalTranscript,
    required Function(String) onError,
  }) async {
    try {
      if (await hasPermission()) {
        _recordingStartTime =
            DateTime.now(); // Track start time for streaming too
        _isRecording = true;
        _isStreamingMode = true;

        print('Starting streaming recording at ${_recordingStartTime}...');

        // Start streaming session
        await AssemblyAIService.startStreaming(
          onTranscript: onTranscript,
          onFinalTranscript: onFinalTranscript,
          onError: onError,
        );

        // Configure for streaming with optimal settings
        const config = RecordConfig(
          encoder: AudioEncoder.pcm16bits,
          sampleRate: 16000,
          numChannels: 1,
          bitRate: 256000,
        );

        // Start recording stream
        final stream = await _recorder.startStream(config);

        // Process audio in 50ms chunks (800 samples at 16kHz)
        const chunkSize = 800 * 2; // 16-bit = 2 bytes per sample
        List<int> buffer = [];

        _streamSubscription = stream.listen(
          (chunk) {
            // Calculate and emit audio level for waveform visualization
            _currentAudioLevel = _calculateAudioLevel(chunk);
            _audioLevelController.add(_currentAudioLevel);

            // Accumulate audio data
            buffer.addAll(chunk);

            // Send chunks when we have enough data
            while (buffer.length >= chunkSize) {
              final chunkToSend = buffer.take(chunkSize).toList();
              buffer = buffer.skip(chunkSize).toList();

              // Send to AssemblyAI streaming
              AssemblyAIService.sendAudioData(Uint8List.fromList(chunkToSend));
            }
          },
          onError: (error) {
            print('Streaming audio error: $error');
            onError('Audio streaming error: $error');
          },
        );

        print('Streaming recording started successfully');
      } else {
        throw Exception('Microphone permission not granted');
      }
    } catch (e) {
      _isRecording = false;
      _isStreamingMode = false;
      _recordingStartTime = null;
      throw Exception('Failed to start streaming recording: $e');
    }
  }

  // Stop recording and return file path (regular mode)
  static Future<String?> stopRecording() async {
    try {
      if (_isRecording && !_isStreamingMode) {
        final path = await _recorder.stop();

        // Calculate actual recording duration
        if (_recordingStartTime != null) {
          _lastRecordingDuration = DateTime.now().difference(
            _recordingStartTime!,
          );
          print(
            'Recording duration: ${_lastRecordingDuration!.inSeconds} seconds',
          );
        }

        _isRecording = false;
        _recordingPath = path;
        _recordingStartTime = null;

        // Reset audio level to zero when recording stops
        _currentAudioLevel = 0.0;
        _audioLevelController.add(_currentAudioLevel);

        print('Recording stopped: $path');

        // Quick validation for speed
        if (path != null) {
          final file = File(path);
          if (await file.exists()) {
            final size = await file.length();
            print('Audio file size: $size bytes');
          }
        }

        return path;
      }
      return null;
    } catch (e) {
      _isRecording = false;
      _recordingStartTime = null;
      _lastRecordingDuration = null;
      throw Exception('Failed to stop recording: $e');
    }
  }

  // Stop streaming recording
  static Future<void> stopStreamingRecording() async {
    try {
      if (_isRecording && _isStreamingMode) {
        // Calculate streaming duration
        if (_recordingStartTime != null) {
          _lastRecordingDuration = DateTime.now().difference(
            _recordingStartTime!,
          );
          print(
            'Streaming duration: ${_lastRecordingDuration!.inSeconds} seconds',
          );
        }

        // Cancel stream subscription
        await _streamSubscription?.cancel();
        _streamSubscription = null;

        // Cancel timer if running
        _chunkTimer?.cancel();
        _chunkTimer = null;

        // Stop recorder
        await _recorder.stop();

        // Stop AssemblyAI streaming
        await AssemblyAIService.stopStreaming();

        _isRecording = false;
        _isStreamingMode = false;
        _recordingStartTime = null;

        // Reset audio level to zero when streaming stops
        _currentAudioLevel = 0.0;
        _audioLevelController.add(_currentAudioLevel);

        print('Streaming recording stopped');
      }
    } catch (e) {
      _isRecording = false;
      _isStreamingMode = false;
      _recordingStartTime = null;
      _lastRecordingDuration = null;
      throw Exception('Failed to stop streaming recording: $e');
    }
  }

  // Universal stop method
  static Future<String?> stopCurrentRecording() async {
    if (_isStreamingMode) {
      await stopStreamingRecording();
      return null;
    } else {
      return await stopRecording();
    }
  }

  // Check if microphone permission is granted
  static Future<bool> hasPermission() async {
    try {
      return await _recorder.hasPermission();
    } catch (e) {
      print('Permission check failed: $e');
      // On Windows desktop, assume permission is granted
      return Platform.isWindows;
    }
  }

  // Upload audio file (MP3/WAV)
  static Future<String?> uploadAudioFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['mp3', 'wav', 'm4a', 'aac', 'flac'],
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        final String filePath = result.files.single.path!;
        final File file = File(filePath);

        // Validate file exists and has content
        if (!await file.exists()) {
          throw Exception('Selected file does not exist');
        }

        final int fileSize = await file.length();
        if (fileSize == 0) {
          throw Exception('Selected file is empty');
        }

        // Check file size (limit to 25MB for AssemblyAI)
        if (fileSize > 25 * 1024 * 1024) {
          throw Exception('File size too large. Maximum size is 25MB');
        }

        print(
          'Audio file selected: $filePath (${(fileSize / 1024 / 1024).toStringAsFixed(2)} MB)',
        );
        return filePath;
      }

      return null; // User cancelled
    } catch (e) {
      throw Exception('Failed to upload audio file: $e');
    }
  }

  // Clean up temporary audio files
  static Future<void> cleanupTempFiles() async {
    try {
      final directory = await getTemporaryDirectory();
      final files = directory.listSync().where(
        (file) => file.path.contains('voice_') && file.path.endsWith('.wav'),
      );

      for (final file in files) {
        if (file is File) {
          await file.delete();
        }
      }
    } catch (e) {
      print('Failed to cleanup temp files: $e');
    }
  }

  // Dispose resources
  static Future<void> dispose() async {
    try {
      if (_isRecording) {
        if (_isStreamingMode) {
          await stopStreamingRecording();
        } else {
          await stopRecording();
        }
      }

      await _streamSubscription?.cancel();
      _chunkTimer?.cancel();
      await _audioLevelController.close();
      _recorder.dispose();
    } catch (e) {
      print('Failed to dispose audio service: $e');
    }
  }
}
