import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:dio/dio.dart';
import 'api_config.dart';
import 'auth_service.dart';

class TranscriptionOptions {
  final String? languageCode;
  final bool punctuate;
  final bool formatText;
  final bool speakerLabels;
  final bool sentimentAnalysis;
  final bool entityDetection;
  final bool autoChapters;
  final bool autoHighlights;
  final bool summarization;

  TranscriptionOptions({
    this.languageCode = 'en',
    this.punctuate = true,
    this.formatText = true,
    this.speakerLabels = false,
    this.sentimentAnalysis = false,
    this.entityDetection = false,
    this.autoChapters = false,
    this.autoHighlights = false,
    this.summarization = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'language_code': languageCode,
      'punctuate': punctuate,
      'format_text': formatText,
      'speaker_labels': speakerLabels,
      'sentiment_analysis': sentimentAnalysis,
      'entity_detection': entityDetection,
      'auto_chapters': autoChapters,
      'auto_highlights': autoHighlights,
      'summarization': summarization,
    };
  }
}

class TranscriptionApiService {
  static final Dio _dio = Dio(
    BaseOptions(
      connectTimeout: ApiConfig.connectTimeout,
      receiveTimeout: ApiConfig.receiveTimeout,
    ),
  );

  // Test server connectivity
  static Future<Map<String, dynamic>> testConnection() async {
    try {
      print('🔍 Testing connection to: ${ApiConfig.baseUrl}');
      final response = await http.get(Uri.parse('${ApiConfig.baseUrl}/health'));
      print(
        '🔍 Health check response: ${response.statusCode} - ${response.body}',
      );

      if (response.statusCode == 200) {
        return {'success': true, 'message': 'Server is running'};
      } else {
        return {
          'success': false,
          'error': 'Server returned ${response.statusCode}',
        };
      }
    } catch (e) {
      print('❌ Connection test failed: $e');
      return {'success': false, 'error': 'Cannot connect to server: $e'};
    }
  }

  // Upload audio file
  static Future<Map<String, dynamic>> uploadFile(File file) async {
    try {
      final token = await AuthService.instance.getAccessToken();
      if (token == null) {
        return {'success': false, 'error': 'Not authenticated'};
      }

      // Create form data
      FormData formData = FormData.fromMap({
        'file': await MultipartFile.fromFile(
          file.path,
          filename: file.path.split('/').last,
        ),
      });

      final response = await _dio.post(
        '${ApiConfig.baseUrl}${ApiConfig.uploadEndpoint}',
        data: formData,
        options: Options(headers: {'Authorization': 'Bearer $token'}),
        onSendProgress: (sent, total) {
          print('Upload progress: ${(sent / total * 100).toStringAsFixed(2)}%');
        },
      );

      if (response.statusCode == 200) {
        return {'success': true, 'data': response.data};
      } else {
        return {'success': false, 'error': 'Upload failed'};
      }
    } on DioException catch (e) {
      return {'success': false, 'error': 'Network error: ${e.message}'};
    } catch (e) {
      return {'success': false, 'error': 'Unexpected error: $e'};
    }
  }

  // Submit transcription
  static Future<Map<String, dynamic>> submitTranscription({
    required String audioUrl,
    required String audioFilename,
    double? estimatedDurationMinutes,
    TranscriptionOptions? options,
  }) async {
    try {
      final token = await AuthService.instance.getAccessToken();
      if (token == null) {
        return {'success': false, 'error': 'Not authenticated'};
      }

      final headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      };

      final requestBody = {
        'audio_url': audioUrl,
        'audio_filename': audioFilename,
        'estimated_duration_minutes': estimatedDurationMinutes,
        'options': options?.toJson(),
      };

      print(
        '📤 Submitting transcription to: ${ApiConfig.baseUrl}${ApiConfig.submitEndpoint}',
      );
      print('📤 Request body: $requestBody');

      final response = await http.post(
        Uri.parse('${ApiConfig.baseUrl}${ApiConfig.submitEndpoint}'),
        headers: headers,
        body: jsonEncode(requestBody),
      );

      print('📥 Submit response status: ${response.statusCode}');
      print('📥 Submit response body: ${response.body}');

      if (response.statusCode == 201) {
        if (response.body.isEmpty) {
          return {'success': false, 'error': 'Server returned empty response'};
        }
        final data = jsonDecode(response.body);
        return {'success': true, 'data': data};
      } else {
        if (response.body.isEmpty) {
          return {
            'success': false,
            'error': 'Server error (${response.statusCode}): Empty response',
          };
        }
        try {
          final error = jsonDecode(response.body);
          return {
            'success': false,
            'error': error['detail'] ?? 'Submission failed',
          };
        } catch (e) {
          return {
            'success': false,
            'error': 'Server error (${response.statusCode}): ${response.body}',
          };
        }
      }
    } catch (e) {
      print('❌ Submit transcription error: $e');
      print('❌ Error type: ${e.runtimeType}');

      // Provide more specific error messages
      String errorMessage;
      if (e.toString().contains('SocketException') ||
          e.toString().contains('Connection refused') ||
          e.toString().contains('Connection failed')) {
        errorMessage = 'Unable to connect to the server. Please check if the FastAPI server is running on localhost:8000';
      } else if (e.toString().contains('TimeoutException')) {
        errorMessage = 'Server request timed out. The server might be overloaded.';
      } else {
        errorMessage = 'Network error: $e';
      }

      return {'success': false, 'error': errorMessage};
    }
  }

  // Check transcription status
  static Future<Map<String, dynamic>> getTranscriptionStatus(
    int transcriptionId,
  ) async {
    try {
      final token = await AuthService.instance.getAccessToken();
      if (token == null) {
        return {'success': false, 'error': 'Not authenticated'};
      }

      final headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      };

      final url =
          '${ApiConfig.baseUrl}${ApiConfig.statusEndpoint}/$transcriptionId/status';
      print('📤 Checking status at: $url');

      final response = await http.get(Uri.parse(url), headers: headers);

      print('📥 Status response: ${response.statusCode} - ${response.body}');

      if (response.statusCode == 200) {
        if (response.body.isEmpty) {
          return {
            'success': false,
            'error': 'Server returned empty status response',
          };
        }
        final data = jsonDecode(response.body);
        return {'success': true, 'data': data};
      } else {
        if (response.body.isEmpty) {
          return {
            'success': false,
            'error':
                'Status check failed (${response.statusCode}): Empty response',
          };
        }
        try {
          final error = jsonDecode(response.body);
          return {
            'success': false,
            'error': error['detail'] ?? 'Status check failed',
          };
        } catch (e) {
          return {
            'success': false,
            'error':
                'Status check failed (${response.statusCode}): ${response.body}',
          };
        }
      }
    } catch (e) {
      print('❌ Status check error: $e');
      return {'success': false, 'error': 'Network error: $e'};
    }
  }

  // Get transcription results
  static Future<Map<String, dynamic>> getTranscriptionResult(
    int transcriptionId,
  ) async {
    try {
      final token = await AuthService.instance.getAccessToken();
      if (token == null) {
        return {'success': false, 'error': 'Not authenticated'};
      }

      final headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      };

      final url =
          '${ApiConfig.baseUrl}${ApiConfig.resultEndpoint}/$transcriptionId/result';
      print('📤 Getting transcription result from: $url');

      final response = await http.get(Uri.parse(url), headers: headers);

      print('📥 Result response status: ${response.statusCode}');
      print('📥 Result response body: ${response.body}');
      print('📥 Result response length: ${response.body.length}');

      if (response.statusCode == 200) {
        if (response.body.isEmpty) {
          return {
            'success': false,
            'error': 'Server returned empty transcription result',
          };
        }

        try {
          final data = jsonDecode(response.body);
          print('📥 Parsed result data: $data');

          // Check if the transcription text exists
          if (data['text'] == null || data['text'].toString().isEmpty) {
            return {
              'success': false,
              'error': 'Transcription completed but no text was generated',
            };
          }

          return {'success': true, 'data': data};
        } catch (e) {
          print('❌ Failed to parse result JSON: $e');
          return {
            'success': false,
            'error': 'Invalid JSON response: ${response.body}',
          };
        }
      } else {
        if (response.body.isEmpty) {
          return {
            'success': false,
            'error':
                'Result retrieval failed (${response.statusCode}): Empty response',
          };
        }
        try {
          final error = jsonDecode(response.body);
          return {
            'success': false,
            'error': error['detail'] ?? 'Result retrieval failed',
          };
        } catch (e) {
          return {
            'success': false,
            'error':
                'Result retrieval failed (${response.statusCode}): ${response.body}',
          };
        }
      }
    } catch (e) {
      print('❌ Get result error: $e');
      return {'success': false, 'error': 'Network error: $e'};
    }
  }

  // Get transcription history
  static Future<Map<String, dynamic>> getTranscriptionHistory({
    int skip = 0,
    int limit = 20,
    String? status,
    int maxRetries = 3,
  }) async {
    int retryCount = 0;

    while (retryCount < maxRetries) {
      try {
        final token = await AuthService.instance.getAccessToken();
        if (token == null) {
          return {'success': false, 'error': 'Not authenticated'};
        }

        final headers = {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        };

        final queryParams = <String, String>{
          'skip': skip.toString(),
          'limit': limit.toString(),
        };
        if (status != null) queryParams['status'] = status;

        final uri = Uri.parse(
          '${ApiConfig.baseUrl}${ApiConfig.historyEndpoint}',
        ).replace(queryParameters: queryParams);

        print(
          '📤 Getting history (attempt ${retryCount + 1}/$maxRetries): $uri',
        );

        // Add timeout to prevent hanging requests
        final response = await http
            .get(uri, headers: headers)
            .timeout(
              const Duration(seconds: 15),
              onTimeout: () {
                throw Exception(
                  'Request timeout - server took too long to respond',
                );
              },
            );

        print('📥 History response status: ${response.statusCode}');

        if (response.statusCode == 200) {
          if (response.body.isEmpty) {
            throw Exception('Server returned empty response');
          }

          try {
            final data = jsonDecode(response.body);

            // Debug: Log the raw API response to identify text truncation
            if (data['transcriptions'] is List) {
              final transcriptions = data['transcriptions'] as List;
              print('🔍 API returned ${transcriptions.length} transcriptions');

              for (int i = 0; i < transcriptions.length && i < 3; i++) {
                final item = transcriptions[i];
                final text =
                    item['transcript_text'] ??
                    item['text'] ??
                    item['transcription'] ??
                    item['result'];
                final wordCount =
                    item['word_count'] ??
                    item['words_count'] ??
                    item['total_words'];

                if (text != null) {
                  final textLength = text.toString().length;
                  final calculatedWords = text
                      .toString()
                      .trim()
                      .split(RegExp(r'\s+'))
                      .length;
                  print(
                    '🔍 Item $i: text_length=$textLength, calculated_words=$calculatedWords, server_word_count=$wordCount',
                  );
                  print(
                    '🔍 Item $i text preview: "${text.toString().substring(0, text.toString().length > 100 ? 100 : text.toString().length)}..."',
                  );

                  if (textLength > 100 &&
                      !text.toString().trim().endsWith('.') &&
                      !text.toString().trim().endsWith('!') &&
                      !text.toString().trim().endsWith('?')) {
                    print(
                      '⚠️ Item $i: Text appears truncated (no ending punctuation)',
                    );
                  }
                }
              }
            }

            print(
              '✅ History loaded successfully: ${data['total'] ?? 'unknown'} total items',
            );
            return {'success': true, 'data': data};
          } catch (e) {
            throw Exception('Invalid JSON response: ${response.body}');
          }
        } else if (response.statusCode == 401) {
          // Authentication error - don't retry
          return {
            'success': false,
            'error': 'Authentication failed. Please log in again.',
          };
        } else if (response.statusCode == 429) {
          // Rate limiting - retry with longer delay
          print('⚠️ Rate limited by server, will retry with delay');

          // Check if server provides Retry-After header
          int retryAfterSeconds = 5; // Default delay
          final retryAfterHeader =
              response.headers['retry-after'] ??
              response.headers['Retry-After'];
          if (retryAfterHeader != null) {
            retryAfterSeconds = int.tryParse(retryAfterHeader) ?? 5;
            retryAfterSeconds = retryAfterSeconds.clamp(
              5,
              60,
            ); // Between 5-60 seconds
          }

          throw Exception('Rate limited (retry after ${retryAfterSeconds}s)');
        } else if (response.statusCode >= 500) {
          // Server error - can retry
          throw Exception('Server error (${response.statusCode})');
        } else {
          // Other client errors - parse error message but don't retry
          try {
            final error = jsonDecode(response.body);
            return {
              'success': false,
              'error': error['detail'] ?? 'History retrieval failed',
            };
          } catch (e) {
            return {
              'success': false,
              'error':
                  'History retrieval failed (${response.statusCode}): ${response.body}',
            };
          }
        }
      } catch (e) {
        retryCount++;
        print('❌ History request failed (attempt $retryCount/$maxRetries): $e');

        if (retryCount >= maxRetries) {
          // All retries exhausted
          String errorMessage =
              'Failed to load history after $maxRetries attempts';

          if (e.toString().contains('Rate limited')) {
            errorMessage =
                'Server is busy. Please wait a moment and try again.';
          } else if (e.toString().contains('timeout')) {
            errorMessage =
                'Connection timeout. Please check your internet connection.';
          } else if (e.toString().contains('network') ||
              e.toString().contains('connection')) {
            errorMessage =
                'Network connection error. Please check your internet.';
          } else if (e.toString().contains('SocketException')) {
            errorMessage =
                'Unable to connect to server. Please try again later.';
          }

          return {'success': false, 'error': errorMessage};
        } else {
          // Wait before retrying with appropriate delay
          int delaySeconds;

          if (e.toString().contains('Rate limited')) {
            // Extract retry delay from rate limiting error
            final match = RegExp(
              r'retry after (\d+)s',
            ).firstMatch(e.toString());
            if (match != null) {
              delaySeconds =
                  int.parse(match.group(1)!) +
                  retryCount; // Add extra delay for subsequent retries
            } else {
              delaySeconds = (5 * retryCount).clamp(
                5,
                30,
              ); // Exponential backoff for rate limiting
            }
            print(
              '⏳ Rate limited - waiting $delaySeconds seconds before retry...',
            );
          } else {
            // Regular exponential backoff for other errors
            delaySeconds = (retryCount * 2).clamp(1, 8);
            print('⏳ Retrying in $delaySeconds seconds...');
          }

          await Future.delayed(Duration(seconds: delaySeconds));
        }
      }
    }

    // This should never be reached, but just in case
    return {'success': false, 'error': 'Unexpected error occurred'};
  }
}
