import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';

class AuthService {
  // Change this to your actual API URL
  // static const String baseUrl = 'https://your-api-domain.com'; // Change for production
  // For development:
  static const String baseUrl = 'http://localhost:8000';

  // Set to true for offline testing (bypasses API calls)
  static const bool debugMode = true;

  static const String _accessTokenKey = 'access_token';
  static const String _refreshTokenKey = 'refresh_token';
  static const String _userKey = 'user_data';

  static AuthService? _instance;
  static AuthService get instance => _instance ??= AuthService._();
  AuthService._();

  // Get headers with authorization
  Future<Map<String, String>> _getHeaders({bool includeAuth = true}) async {
    final headers = {'Content-Type': 'application/json'};

    if (includeAuth) {
      final token = await getAccessToken();
      if (token != null) {
        headers['Authorization'] = 'Bearer $token';
      }
    }

    return headers;
  }

  // Handle API errors
  void _handleError(http.Response response) {
    try {
      final Map<String, dynamic> errorData = json.decode(response.body);

      // Handle FastAPI validation errors (422)
      if (response.statusCode == 422 && errorData.containsKey('detail')) {
        final details = errorData['detail'];
        if (details is List && details.isNotEmpty) {
          // Extract the first validation error message
          final firstError = details[0];
          if (firstError is Map && firstError.containsKey('msg')) {
            throw Exception(firstError['msg']);
          }
        }
        throw Exception('Validation error');
      }

      // Handle other error formats
      if (errorData.containsKey('message')) {
        throw Exception(errorData['message']);
      } else if (errorData.containsKey('detail') &&
          errorData['detail'] is String) {
        throw Exception(errorData['detail']);
      } else {
        throw Exception('Server error: ${response.statusCode}');
      }
    } catch (e) {
      // If it's already an Exception we threw, re-throw it
      if (e is Exception) {
        rethrow;
      }
      // If JSON parsing fails, show a generic error
      throw Exception('Server error (${response.statusCode})');
    }
  }

  // Register new user
  Future<User> register(RegisterRequest request) async {
    if (debugMode) {
      // Return mock user for testing
      await Future.delayed(
        const Duration(seconds: 1),
      ); // Simulate network delay
      return User(
        userId: 'debug_user_123',
        email: request.email,
        fullName: '${request.firstName} ${request.lastName}',
        plan: 'free',
        creditsRemaining: 60,
        emailVerified: false,
        createdAt: DateTime.now(),
      );
    }

    try {
      final requestBody = request.toJson();
      print('DEBUG: Registration request: $requestBody'); // Debug log

      final response = await http.post(
        Uri.parse('$baseUrl/register'),
        headers: await _getHeaders(includeAuth: false),
        body: json.encode(requestBody),
      );

      print('DEBUG: Response status: ${response.statusCode}'); // Debug log
      print('DEBUG: Response body: ${response.body}'); // Debug log

      if (response.statusCode == 201) {
        final data = json.decode(response.body);
        print('DEBUG: Parsed response data: $data'); // Debug log
        return User.fromJson(data);
      } else {
        _handleError(response);
        throw Exception('Registration failed');
      }
    } catch (e) {
      print('DEBUG: Registration error: $e'); // Debug log
      if (e.toString().contains('SocketException') ||
          e.toString().contains('Failed host lookup')) {
        throw Exception(
          'Cannot connect to server. Please check if the API server is running at $baseUrl',
        );
      } else if (e.toString().contains('TimeoutException')) {
        throw Exception(
          'Connection timeout. Please check your internet connection.',
        );
      } else {
        // Re-throw the original exception if it's already formatted
        rethrow;
      }
    }
  }

  // Login user
  Future<AuthResponse> login(LoginRequest request) async {
    if (debugMode) {
      // Return mock auth response for testing
      await Future.delayed(
        const Duration(seconds: 1),
      ); // Simulate network delay
      final user = User(
        userId: 'debug_user_123',
        email: request.email,
        fullName: 'Debug User',
        plan: 'free',
        creditsRemaining: 60,
        emailVerified: true,
        createdAt: DateTime.now(),
      );

      const accessToken = 'debug_access_token';
      const refreshToken = 'debug_refresh_token';

      await _saveTokens(accessToken, refreshToken);
      await _saveUser(user);

      return AuthResponse(
        accessToken: accessToken,
        refreshToken: refreshToken,
        expiresIn: 1800,
        user: user,
      );
    }

    try {
      final response = await http.post(
        Uri.parse('$baseUrl/login'),
        headers: await _getHeaders(includeAuth: false),
        body: json.encode(request.toJson()),
      );

      if (response.statusCode == 200) {
        final authResponse = AuthResponse.fromJson(json.decode(response.body));
        await _saveTokens(authResponse.accessToken, authResponse.refreshToken);
        await _saveUser(authResponse.user);
        return authResponse;
      } else {
        _handleError(response);
        throw Exception('Login failed');
      }
    } catch (e) {
      if (e.toString().contains('SocketException') ||
          e.toString().contains('Failed host lookup')) {
        throw Exception(
          'Cannot connect to server. Please check if the API server is running at $baseUrl',
        );
      } else if (e.toString().contains('TimeoutException')) {
        throw Exception(
          'Connection timeout. Please check your internet connection.',
        );
      } else {
        // Re-throw the original exception if it's already formatted
        rethrow;
      }
    }
  }

  // Refresh access token
  Future<bool> refreshToken() async {
    final refreshToken = await getRefreshToken();
    if (refreshToken == null) return false;

    try {
      final response = await http.post(
        Uri.parse('$baseUrl/refresh'),
        headers: await _getHeaders(includeAuth: false),
        body: json.encode({'refresh_token': refreshToken}),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        await _saveTokens(data['access_token'], data['refresh_token']);
        return true;
      }
    } catch (e) {
      // Handle error silently and return false
    }

    return false;
  }

  // Logout user
  Future<void> logout() async {
    try {
      await http.post(
        Uri.parse('$baseUrl/logout'),
        headers: await _getHeaders(),
      );
    } catch (e) {
      // Continue with local logout even if API call fails
    } finally {
      await _clearTokens();
    }
  }

  // Get current user
  Future<User?> getCurrentUser() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/me'),
        headers: await _getHeaders(),
      );

      if (response.statusCode == 200) {
        final user = User.fromJson(json.decode(response.body));
        await _saveUser(user);
        return user;
      } else if (response.statusCode == 401) {
        // Try to refresh token
        if (await refreshToken()) {
          return getCurrentUser(); // Retry with new token
        }
      }
    } catch (e) {
      // Return cached user if API fails
      return getCachedUser();
    }

    return null;
  }

  // Token storage methods
  Future<void> _saveTokens(String accessToken, String refreshToken) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_accessTokenKey, accessToken);
    await prefs.setString(_refreshTokenKey, refreshToken);
  }

  Future<String?> getAccessToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_accessTokenKey);
  }

  Future<String?> getRefreshToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_refreshTokenKey);
  }

  Future<void> _clearTokens() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_accessTokenKey);
    await prefs.remove(_refreshTokenKey);
    await prefs.remove(_userKey);
  }

  // User storage methods
  Future<void> _saveUser(User user) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userKey, json.encode(user.toJson()));
  }

  Future<User?> getCachedUser() async {
    final prefs = await SharedPreferences.getInstance();
    final userJson = prefs.getString(_userKey);
    if (userJson != null) {
      return User.fromJson(json.decode(userJson));
    }
    return null;
  }

  // Check if user is logged in
  Future<bool> isLoggedIn() async {
    final token = await getAccessToken();
    return token != null;
  }

  // Make authenticated API call with auto token refresh
  Future<http.Response> authenticatedRequest(
    String method,
    String endpoint, {
    Map<String, dynamic>? body,
    Map<String, String>? additionalHeaders,
  }) async {
    final headers = await _getHeaders();
    if (additionalHeaders != null) {
      headers.addAll(additionalHeaders);
    }

    final uri = Uri.parse('$baseUrl$endpoint');
    http.Response response;

    switch (method.toUpperCase()) {
      case 'GET':
        response = await http.get(uri, headers: headers);
        break;
      case 'POST':
        response = await http.post(
          uri,
          headers: headers,
          body: body != null ? json.encode(body) : null,
        );
        break;
      case 'PUT':
        response = await http.put(
          uri,
          headers: headers,
          body: body != null ? json.encode(body) : null,
        );
        break;
      case 'DELETE':
        response = await http.delete(uri, headers: headers);
        break;
      default:
        throw Exception('Unsupported HTTP method: $method');
    }

    // If unauthorized, try to refresh token and retry
    if (response.statusCode == 401) {
      if (await refreshToken()) {
        final newHeaders = await _getHeaders();
        if (additionalHeaders != null) {
          newHeaders.addAll(additionalHeaders);
        }

        switch (method.toUpperCase()) {
          case 'GET':
            response = await http.get(uri, headers: newHeaders);
            break;
          case 'POST':
            response = await http.post(
              uri,
              headers: newHeaders,
              body: body != null ? json.encode(body) : null,
            );
            break;
          case 'PUT':
            response = await http.put(
              uri,
              headers: newHeaders,
              body: body != null ? json.encode(body) : null,
            );
            break;
          case 'DELETE':
            response = await http.delete(uri, headers: newHeaders);
            break;
        }
      }
    }

    return response;
  }
}
