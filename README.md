# Flutter Voice Assistant

A Windows desktop voice assistant app that captures audio, transcribes it using AssemblyAI's Speech-to-Text API, and automatically pastes the transcribed text into any application - similar to WisprFlow.

## Features

- **Voice Recording**: Click the microphone button to start/stop recording
- **Real-time Transcription**: Uses AssemblyAI API for accurate speech-to-text conversion
- **Auto-paste**: Automatically pastes transcribed text to the active window
- **Manual Controls**: Copy and paste transcribed text manually
- **Modern UI**: Beautiful dark theme with gradient background
- **Status Indicators**: Visual feedback for recording, processing, and completion states

## Setup

### Prerequisites

- Flutter SDK (>=3.0.0)
- Windows 10/11
- AssemblyAI API key

### Installation

1. Clone or download this project
2. Navigate to the project directory:

   ```bash
   cd flutter_voice_assistant
   ```

3. Install dependencies:

   ```bash
   flutter pub get
   ```

4. Update your AssemblyAI API key in `lib/services/assembly_ai_service.dart`:

   ```dart
   static const String _apiKey = 'YOUR_ASSEMBLYAI_API_KEY_HERE';
   ```

5. Run the application:
   ```bash
   flutter run -d windows
   ```

## Usage

1. **Start Recording**: Click the blue microphone button to start recording your voice
2. **Stop Recording**: Click the red stop button to end recording
3. **Processing**: The app will upload and transcribe your audio (may take a few seconds)
4. **Auto-paste**: If enabled, the transcribed text will automatically be pasted to your active window
5. **Manual Controls**:
   - Toggle auto-paste on/off with the switch
   - Use Copy button to copy text to clipboard
   - Use Paste button to paste text to active window
   - Use Clear button to reset the transcription

## API Configuration

This app uses AssemblyAI for speech-to-text conversion. You'll need to:

1. Sign up at [AssemblyAI](https://www.assemblyai.com/)
2. Get your API key from the dashboard
3. Replace the API key in `lib/services/assembly_ai_service.dart`

## File Structure

```
lib/
├── main.dart                          # App entry point
├── screens/
│   └── main_screen.dart              # Main UI screen
├── providers/
│   └── transcription_provider.dart   # State management
└── services/
    ├── assembly_ai_service.dart      # AssemblyAI API integration
    ├── audio_service.dart            # Audio recording service
    └── clipboard_service.dart        # Windows clipboard operations
```

## Dependencies

- `http`: API requests to AssemblyAI
- `record`: Audio recording functionality
- `path_provider`: File system access
- `win32`: Windows API integration
- `provider`: State management
- `ffi`: Foreign function interface for Windows APIs

## Troubleshooting

### Microphone Permission

- Make sure your app has microphone permissions
- Check Windows privacy settings for microphone access

### API Issues

- Verify your AssemblyAI API key is valid
- Check your internet connection
- Ensure API key has sufficient credits

### Windows Integration

- The app needs to run with appropriate permissions for clipboard and keyboard simulation
- Some antivirus software may flag the keyboard simulation functionality

## Building for Release

To build a Windows executable:

```bash
flutter build windows --release
```

The executable will be in `build/windows/runner/Release/`

## License

This project is for educational purposes. Make sure to comply with AssemblyAI's terms of service when using their API.
