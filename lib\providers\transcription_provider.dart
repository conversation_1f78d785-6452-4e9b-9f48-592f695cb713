import 'package:flutter/foundation.dart';
import 'package:file_picker/file_picker.dart';
import '../services/audio_service.dart';
import '../services/clipboard_service.dart';
import '../services/transcription_api_service.dart';
import '../services/streaming_transcription_service.dart';
import '../services/global_hotkey_service.dart';
import '../providers/auth_provider.dart';
import '../providers/history_provider.dart';
import '../providers/settings_provider.dart';
import 'dart:io';
import 'dart:async';

enum TranscriptionStatus {
  idle,
  recording,
  processing,
  uploading,
  completed,
  error,
  streaming,
}

class TranscriptionProvider extends ChangeNotifier {
  TranscriptionStatus _status = TranscriptionStatus.idle;
  String _transcriptionText = '';
  String _streamingText = '';
  String _accumulatedStreamingText = '';
  String _currentPartialText = '';
  String _errorMessage = '';
  bool _autoVocalize = true;
  bool _useStreaming = false;
  bool _globalHotkeyEnabled = true;
  bool _isGlobalHotkeySession = false;

  // Reference to auth provider for credit updates
  AuthProvider? _authProvider;
  HistoryProvider? _historyProvider;
  SettingsProvider? _settingsProvider;
  String? _lastRecordingPath;
  DateTime? _processingStartTime;
  String _lastProcessingTime = '';
  Timer? _statusPollingTimer;
  int? _currentTranscriptionId;
  File? _selectedAudioFile;
  String? _uploadUrl;

  // Streaming service
  final StreamingTranscriptionService _streamingService =
      StreamingTranscriptionService();
  StreamSubscription? _streamingSubscription;

  TranscriptionStatus get status => _status;
  String get transcriptionText => _transcriptionText;
  String get streamingText => _streamingText;
  String get errorMessage => _errorMessage;
  bool get autoVocalize => _autoVocalize;
  bool get useStreaming => _useStreaming;
  bool get globalHotkeyEnabled => _globalHotkeyEnabled;
  bool get isRecording =>
      _status == TranscriptionStatus.recording ||
      _status == TranscriptionStatus.streaming;
  bool get isProcessing =>
      _status == TranscriptionStatus.processing ||
      _status == TranscriptionStatus.uploading;
  bool get isStreaming => _status == TranscriptionStatus.streaming;
  bool get hasRecording =>
      _lastRecordingPath != null && File(_lastRecordingPath!).existsSync();
  bool get hasSelectedFile => _selectedAudioFile != null;
  String get lastProcessingTime => _lastProcessingTime;

  // Get current display text based on mode
  String get displayText {
    if (_useStreaming && isStreaming) {
      return _streamingText.isEmpty ? 'Listening... speak now' : _streamingText;
    }
    return _transcriptionText;
  }

  // Get streaming display text
  String get streamingDisplayText {
    if (isStreaming) {
      return _streamingText.isEmpty
          ? 'Listening... start speaking'
          : _streamingText;
    }
    return '';
  }

  @override
  void dispose() {
    _statusPollingTimer?.cancel();
    _streamingSubscription?.cancel();
    _streamingService.dispose();
    GlobalHotkeyService.instance.unregisterHotkey();
    _settingsProvider?.removeListener(_updateHotkeys);
    super.dispose();
  }

  // Set settings provider reference for hotkey configuration
  void setSettingsProvider(SettingsProvider settingsProvider) {
    _settingsProvider = settingsProvider;
    // Update hotkeys when settings change
    _settingsProvider?.addListener(_updateHotkeys);
    // Update hotkeys immediately
    _updateHotkeys();
  }

  // Update hotkeys based on settings
  void _updateHotkeys() {
    if (_settingsProvider != null) {
      final primaryHotkey = _settingsProvider!.primaryHotkey;
      final secondaryHotkey = _settingsProvider!.secondaryHotkey;

      // Update the global hotkey service with new settings
      GlobalHotkeyService.instance.updateHotkeys(
        primaryHotkey,
        secondaryHotkey,
      );

      print(
        '🔧 Updated hotkeys from settings: Primary: ${primaryHotkey.join('+')} | Secondary: ${secondaryHotkey.join('+')}',
      );
    }
  }

  // Initialize global hotkey service
  Future<void> initializeGlobalHotkey() async {
    try {
      // Get hotkey settings if available
      List<String>? primaryHotkey;
      List<String>? secondaryHotkey;

      if (_settingsProvider != null) {
        primaryHotkey = _settingsProvider!.primaryHotkey;
        secondaryHotkey = _settingsProvider!.secondaryHotkey;
      }

      await GlobalHotkeyService.instance.registerHotkey(
        onKeyPressed: _onGlobalHotkeyPressed,
        onKeyReleased: _onGlobalHotkeyReleased,
        primaryHotkey: primaryHotkey,
        secondaryHotkey: secondaryHotkey,
      );
      print('✅ Global hotkey service initialized');
    } catch (e) {
      print('❌ Failed to initialize global hotkey service: $e');
    }
  }

  // Handle global hotkey pressed - start streaming
  void _onGlobalHotkeyPressed() {
    print(
      '🔥 _onGlobalHotkeyPressed called - globalHotkeyEnabled: $_globalHotkeyEnabled, isRecording: $isRecording',
    );

    // Only proceed if global hotkey is enabled and not already recording
    if (_globalHotkeyEnabled && !isRecording) {
      // If not in streaming mode, automatically switch to streaming mode
      if (!_useStreaming) {
        print('🔄 Global hotkey pressed - auto-switching to streaming mode...');
        _useStreaming = true;
        notifyListeners();
      }

      _isGlobalHotkeySession = true;
      print('🎤 Global hotkey pressed - starting streaming...');
      startStreamingRecording();
    } else {
      print(
        '🚫 Global hotkey ignored - globalHotkeyEnabled: $_globalHotkeyEnabled, isRecording: $isRecording',
      );
    }
  }

  // Handle global hotkey released - stop streaming
  void _onGlobalHotkeyReleased() {
    print(
      '🔥 _onGlobalHotkeyReleased called - globalHotkeyEnabled: $_globalHotkeyEnabled, isStreaming: $isStreaming',
    );

    // Only stop if global hotkey is enabled and currently streaming
    if (_globalHotkeyEnabled && isStreaming) {
      print('🛑 Global hotkey released - stopping streaming...');
      stopStreamingRecording();
    } else {
      print(
        '🚫 Global hotkey release ignored - globalHotkeyEnabled: $_globalHotkeyEnabled, isStreaming: $isStreaming',
      );
    }
  }

  void setAutoVocalize(bool value) {
    _autoVocalize = value;
    notifyListeners();
  }

  void setUseStreaming(bool value) {
    _useStreaming = value;
    notifyListeners();
  }

  void setGlobalHotkeyEnabled(bool value) {
    _globalHotkeyEnabled = value;
    notifyListeners();
  }

  // Set auth provider reference for credit updates
  void setAuthProvider(AuthProvider authProvider) {
    _authProvider = authProvider;
  }

  // Set history provider reference for history updates
  void setHistoryProvider(HistoryProvider historyProvider) {
    _historyProvider = historyProvider;
  }

  // Refresh user credits after transcription (throttled)
  Future<void> _refreshCredits() async {
    if (_authProvider != null) {
      print('🔄 Refreshing credits after transcription...');
      // Use the immediate refresh method that bypasses throttling
      await _authProvider!.refreshCreditsAfterTranscriptionComplete();
    }
  }

  // Refresh history after transcription
  Future<void> _refreshHistory() async {
    if (_historyProvider != null) {
      print('🔄 Refreshing history after transcription...');
      // Refresh history after transcription with server request
      await _historyProvider!.loadHistory(
        refresh: true,
        afterTranscription: true,
      );
    }
  }

  // Pick audio file for upload
  Future<void> pickAudioFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: [
          'mp3',
          'wav',
          'flac',
          'm4a',
          'aac',
          'ogg',
          'wma',
          'amr',
          '3gp',
          'mp4',
          'mov',
          'avi',
        ],
      );

      if (result != null && result.files.single.path != null) {
        _selectedAudioFile = File(result.files.single.path!);
        _transcriptionText =
            'Selected: ${_selectedAudioFile!.path.split('/').last}';
        notifyListeners();
      }
    } catch (e) {
      _status = TranscriptionStatus.error;
      _errorMessage = 'Failed to pick file: $e';
      notifyListeners();
    }
  }

  // Upload and transcribe selected file
  Future<void> uploadAndTranscribe() async {
    if (_selectedAudioFile == null) {
      _status = TranscriptionStatus.error;
      _errorMessage = 'No file selected';
      notifyListeners();
      return;
    }

    try {
      _processingStartTime = DateTime.now();
      _status = TranscriptionStatus.uploading;
      _errorMessage = '';
      notifyListeners();

      // Upload file
      final uploadResult = await TranscriptionApiService.uploadFile(
        _selectedAudioFile!,
      );

      if (!uploadResult['success']) {
        throw Exception(uploadResult['error']);
      }

      _uploadUrl = uploadResult['data']['upload_url'];

      // Test server connection first
      print('🔍 Testing server connection...');
      final connectionTest = await TranscriptionApiService.testConnection();
      if (!connectionTest['success']) {
        throw Exception('Server connection failed: ${connectionTest['error']}');
      }
      print('✅ Server connection OK');

      // Submit transcription
      _status = TranscriptionStatus.processing;
      notifyListeners();

      final options = TranscriptionOptions(
        languageCode: 'en',
        punctuate: true,
        formatText: true,
        speakerLabels: true,
        sentimentAnalysis: false,
      );

      // Get actual recording duration from AudioService
      final recordingDuration = AudioService.lastRecordingDuration;
      final estimatedDurationMinutes = recordingDuration != null
          ? recordingDuration.inSeconds / 60.0
          : 1.0;

      print(
        '📊 Using actual recording duration: ${recordingDuration?.inSeconds ?? 'unknown'} seconds (${estimatedDurationMinutes.toStringAsFixed(2)} minutes)',
      );

      final submitResult = await TranscriptionApiService.submitTranscription(
        audioUrl: _uploadUrl!,
        audioFilename: _selectedAudioFile!.path.split('/').last,
        estimatedDurationMinutes: estimatedDurationMinutes,
        options: options,
      );

      if (!submitResult['success']) {
        throw Exception(submitResult['error']);
      }

      _currentTranscriptionId = submitResult['data']['transcription_id'];

      // Start polling for status
      _startStatusPolling();
    } catch (e) {
      _status = TranscriptionStatus.error;
      _errorMessage = e.toString().replaceFirst('Exception: ', '');

      if (_processingStartTime != null) {
        final processingDuration = DateTime.now().difference(
          _processingStartTime!,
        );
        _lastProcessingTime =
            '${(processingDuration.inMilliseconds / 1000).toStringAsFixed(2)}s';
      }

      notifyListeners();
    }
  }

  // Start status polling for file transcription
  void _startStatusPolling() {
    _statusPollingTimer = Timer.periodic(Duration(seconds: 3), (timer) async {
      if (_currentTranscriptionId == null) {
        timer.cancel();
        return;
      }

      try {
        final statusResult =
            await TranscriptionApiService.getTranscriptionStatus(
              _currentTranscriptionId!,
            );

        if (statusResult['success']) {
          final statusData = statusResult['data'];
          final status = statusData['status'];

          if (status == 'completed') {
            timer.cancel();

            // Check if transcription text is already in status response
            if (statusData['text'] != null &&
                statusData['text'].toString().isNotEmpty) {
              print('✅ Transcription text found in status response');
              _handleTranscriptionComplete(statusData['text']);
            } else {
              // Get transcription text from separate endpoint
              print('🔍 Getting transcription text from result endpoint');
              await Future.delayed(Duration(seconds: 1));
              await _getTranscriptionResult();
            }
          } else if (status == 'error') {
            timer.cancel();
            _status = TranscriptionStatus.error;
            _errorMessage = 'Transcription failed';

            if (_processingStartTime != null) {
              final processingDuration = DateTime.now().difference(
                _processingStartTime!,
              );
              _lastProcessingTime =
                  '${(processingDuration.inMilliseconds / 1000).toStringAsFixed(2)}s';
            }

            notifyListeners();
          }
        }
      } catch (e) {
        print('Status polling error: $e');
      }
    });
  }

  // Get transcription result
  Future<void> _getTranscriptionResult() async {
    if (_currentTranscriptionId == null) return;

    try {
      print('🔍 Getting transcription result for ID: $_currentTranscriptionId');

      // Update status to show we're getting the result
      _transcriptionText = 'Retrieving transcription text...';
      notifyListeners();

      final resultResponse =
          await TranscriptionApiService.getTranscriptionResult(
            _currentTranscriptionId!,
          );

      if (resultResponse['success']) {
        final resultData = resultResponse['data'];
        print('✅ Got result data: $resultData');

        final transcriptionText =
            resultData['text'] ??
            resultData['transcript_text'] ??
            'No transcription text available';
        _handleTranscriptionComplete(transcriptionText);
      } else {
        throw Exception(resultResponse['error']);
      }
    } catch (e) {
      print('❌ Error getting transcription result: $e');
      _status = TranscriptionStatus.error;
      _errorMessage =
          'Failed to get transcription result: ${e.toString().replaceFirst('Exception: ', '')}';
      notifyListeners();
    }
  }

  // Start recording voice (regular mode)
  Future<void> startRecording() async {
    try {
      _status = TranscriptionStatus.recording;
      _errorMessage = '';
      notifyListeners();

      await AudioService.startRecording();
    } catch (e) {
      _status = TranscriptionStatus.error;
      _errorMessage = e.toString();
      notifyListeners();
      rethrow;
    }
  }

  // Stop recording and process transcription (regular mode)
  Future<void> stopRecording() async {
    try {
      if (_status != TranscriptionStatus.recording) return;

      _processingStartTime = DateTime.now();
      _status = TranscriptionStatus.uploading;
      notifyListeners();

      final audioPath = await AudioService.stopRecording();
      if (audioPath == null) {
        throw Exception('No audio file recorded');
      }

      _lastRecordingPath = audioPath;

      // Upload recorded file
      final file = File(audioPath);
      final uploadResult = await TranscriptionApiService.uploadFile(file);

      if (!uploadResult['success']) {
        throw Exception(uploadResult['error']);
      }

      _uploadUrl = uploadResult['data']['upload_url'];

      // Submit transcription
      _status = TranscriptionStatus.processing;
      notifyListeners();

      final options = TranscriptionOptions(
        languageCode: 'en',
        punctuate: true,
        formatText: true,
        speakerLabels: true,
      );

      // Get actual recording duration from AudioService
      final recordingDuration = AudioService.lastRecordingDuration;
      final estimatedDurationMinutes = recordingDuration != null
          ? recordingDuration.inSeconds / 60.0
          : 1.0;

      print(
        '📊 Using actual recording duration: ${recordingDuration?.inSeconds ?? 'unknown'} seconds (${estimatedDurationMinutes.toStringAsFixed(2)} minutes)',
      );

      final submitResult = await TranscriptionApiService.submitTranscription(
        audioUrl: _uploadUrl!,
        audioFilename: file.path.split('/').last,
        estimatedDurationMinutes: estimatedDurationMinutes,
        options: options,
      );

      if (!submitResult['success']) {
        throw Exception(submitResult['error']);
      }

      _currentTranscriptionId = submitResult['data']['transcription_id'];
      _startStatusPolling();
    } catch (e) {
      _status = TranscriptionStatus.error;
      _errorMessage = e.toString().replaceFirst('Exception: ', '');

      if (_processingStartTime != null) {
        final processingDuration = DateTime.now().difference(
          _processingStartTime!,
        );
        _lastProcessingTime =
            '${(processingDuration.inMilliseconds / 1000).toStringAsFixed(2)}s';
      }

      notifyListeners();
    }
  }

  // Start streaming recording
  Future<void> startStreamingRecording() async {
    print('🚀 startStreamingRecording called - current status: $_status');
    try {
      _status = TranscriptionStatus.streaming;
      _errorMessage = '';
      _transcriptionText = '';
      _streamingText = '';
      _accumulatedStreamingText = '';
      _currentPartialText = '';
      _processingStartTime = DateTime.now();
      notifyListeners();
      print('📊 Status updated to streaming, creating session...');

      // Create streaming session
      print('🏗️ Creating streaming session...');
      final sessionResult = await _streamingService.createSession(
        options: StreamingOptions(
          sampleRate: 16000,
          encoding: 'pcm_s16le',
          channels: 1,
          interimResults: true,
          punctuate: true,
          formatText: true,
          speakerLabels: false,
          languageCode: 'en',
          maxDurationMinutes: 0.1, // 6 seconds = 1 credit
          estimatedCost: 1, // Force 1 credit requirement
        ),
      );

      print('📋 Session creation result: $sessionResult');
      if (!sessionResult['success']) {
        throw Exception(sessionResult['error']);
      }

      // Start streaming
      final streamingResult = await _streamingService.startStreaming();

      if (!streamingResult['success']) {
        throw Exception(streamingResult['error']);
      }

      // Listen to transcription stream
      _streamingSubscription = _streamingService.transcriptionStream?.listen(
        (data) => _handleStreamingMessage(data),
        onError: (error) {
          _status = TranscriptionStatus.error;
          _errorMessage = 'Streaming error: $error';
          notifyListeners();
        },
      );

      // Start audio capture and send to FastAPI WebSocket
      print('🎤 Starting audio capture for FastAPI streaming...');
      await _streamingService.startAudioCapture();

      print('✅ Streaming session started with audio capture');
    } catch (e) {
      print('❌ Error in startStreamingRecording: $e');
      _status = TranscriptionStatus.error;
      _errorMessage = e.toString().replaceFirst('Exception: ', '');
      notifyListeners();

      // Refresh credits even on error (in case some were deducted)
      await _refreshCredits();
    }
  }

  // Handle streaming messages from WebSocket
  void _handleStreamingMessage(Map<String, dynamic> data) {
    final messageType = data['type'] ?? 'unknown';
    print('📨 Handling message type: $messageType');

    switch (messageType) {
      case 'connection_established':
        print('✅ FastAPI connection established: ${data['message']}');
        _streamingText = 'Connected to FastAPI server. Listening for audio...';
        notifyListeners();
        break;

      case 'session_begins':
        print('✅ Streaming session started: ${data['session_id']}');
        _streamingText = 'Streaming session active. Start speaking...';
        notifyListeners();
        break;

      case 'partial_transcript':
        _currentPartialText = data['text'] ?? '';
        _streamingText = _accumulatedStreamingText + _currentPartialText;
        notifyListeners();
        break;

      case 'final_transcript':
        final finalText = data['text'] ?? '';
        if (finalText.isNotEmpty) {
          if (_accumulatedStreamingText.isNotEmpty &&
              !_accumulatedStreamingText.endsWith(' ')) {
            _accumulatedStreamingText += ' ';
          }
          _accumulatedStreamingText += finalText;
          _currentPartialText = '';
          _streamingText = _accumulatedStreamingText;
          notifyListeners();

          // Don't auto-paste here during streaming - wait until hotkey is released
          // This prevents multiple pastes during a single recording session
          print('📝 Accumulated text: $_accumulatedStreamingText');
        }
        break;

      case 'session_terminated':
        print('🔚 Session terminated by server');
        _stopStreamingRecording();
        break;

      case 'error':
        final errorMessage =
            data['message'] ?? data['error'] ?? 'Unknown error';
        print('⚠️ Server error message: $errorMessage');

        // Only treat certain errors as streaming errors that should stop the session
        if (errorMessage.contains('session') ||
            errorMessage.contains('authentication') ||
            errorMessage.contains('connection')) {
          _status = TranscriptionStatus.error;
          _errorMessage = errorMessage;
          notifyListeners();
        } else {
          // Log but don't stop streaming for minor errors like "Unknown command"
          print('ℹ️ Non-critical server message: $errorMessage');
        }
        break;

      default:
        print('ℹ️ Unknown message type received: $messageType - $data');
        break;
    }
  }

  // Stop streaming recording
  Future<void> stopStreamingRecording() async {
    try {
      await _stopStreamingRecording();
    } catch (e) {
      _status = TranscriptionStatus.error;
      _errorMessage = e.toString();
      notifyListeners();
    }
  }

  Future<void> _stopStreamingRecording() async {
    // Get actual recording duration from AudioService instead of processing time
    final recordingDuration = AudioService.lastRecordingDuration;
    if (recordingDuration != null) {
      _lastProcessingTime = '${recordingDuration.inSeconds}s';
      print(
        '📊 Streaming session actual duration: ${recordingDuration.inSeconds} seconds',
      );
    } else {
      // Fallback to processing time if recording duration is not available
      if (_processingStartTime != null) {
        final processingDuration = DateTime.now().difference(
          _processingStartTime!,
        );
        _lastProcessingTime =
            '${(processingDuration.inMilliseconds / 1000).toStringAsFixed(2)}s';
        print('⚠️ Using processing time as fallback: $_lastProcessingTime');
      }
    }

    await _streamingSubscription?.cancel();
    _streamingSubscription = null;

    await _streamingService.stopStreaming();
    // Note: No need to call AudioService.stopRecording() since we're not using direct  streaming

    _status = TranscriptionStatus.completed;

    // Set final transcription text and auto-paste if enabled
    if (_accumulatedStreamingText.isNotEmpty) {
      _transcriptionText = _accumulatedStreamingText;

      // Auto-paste the final transcription when session ends
      if (_autoVocalize) {
        final sessionType = _isGlobalHotkeySession ? 'global hotkey' : 'manual';
        print(
          '📋 Auto-pasting final transcription ($sessionType session): $_accumulatedStreamingText',
        );
        ClipboardService.copyAndPaste(_accumulatedStreamingText);
      }
    }

    // Reset global hotkey session flag
    _isGlobalHotkeySession = false;

    notifyListeners();

    // Delay credit refresh to allow server time to process usage
    // This fixes the issue where usage minutes show as 0 after first streaming
    print(
      '⏳ Delaying credit and history refresh by 3 seconds to allow server processing...',
    );
    await Future.delayed(Duration(seconds: 3));

    // Refresh credits and history after streaming session completes
    // The history refresh will pull the latest data from server including this transcription
    await _refreshCredits();
    await _refreshHistory();

    print('✅ Streaming session completed and history refreshed from server');

    // Note: Removed local cache addition to prevent race conditions
    // The server refresh above will get the latest data including this transcription
  }

  // Clear current transcription
  void clearTranscription() {
    _transcriptionText = '';
    _streamingText = '';
    _accumulatedStreamingText = '';
    _currentPartialText = '';
    _errorMessage = '';
    _selectedAudioFile = null;
    _uploadUrl = null;
    _currentTranscriptionId = null;
    _status = TranscriptionStatus.idle;
    _isGlobalHotkeySession = false;
    _statusPollingTimer?.cancel();
    notifyListeners();
  }

  // Helper method to handle transcription completion
  Future<void> _handleTranscriptionComplete(String transcriptionText) async {
    _transcriptionText = transcriptionText;
    _status = TranscriptionStatus.completed;

    // Use actual recording duration if available, otherwise fall back to processing time
    final recordingDuration = AudioService.lastRecordingDuration;
    if (recordingDuration != null) {
      _lastProcessingTime = '${recordingDuration.inSeconds}s';
      print(
        '📊 File transcription actual duration: ${recordingDuration.inSeconds} seconds',
      );
    } else {
      // Fallback to processing time if recording duration is not available
      if (_processingStartTime != null) {
        final processingDuration = DateTime.now().difference(
          _processingStartTime!,
        );
        _lastProcessingTime =
            '${(processingDuration.inMilliseconds / 1000).toStringAsFixed(2)}s';
        print('⚠️ Using processing time as fallback: $_lastProcessingTime');
      }
    }

    print('✅ Final transcription text: $_transcriptionText');

    // Auto-paste if enabled
    if (_autoVocalize &&
        _transcriptionText.isNotEmpty &&
        _transcriptionText != 'No transcription text available') {
      ClipboardService.copyAndPaste(_transcriptionText);
    }

    notifyListeners();

    // Add delay for recording transcriptions too
    print(
      '⏳ Delaying credit and history refresh by 3 seconds to allow server processing...',
    );
    await Future.delayed(Duration(seconds: 3));

    // Refresh credits and history after transcription completes
    // The history refresh will pull the latest data from server including this transcription
    await _refreshCredits();
    await _refreshHistory();

    print(
      '✅ Recording transcription completed and history refreshed from server',
    );

    // Note: Removed local cache addition to prevent race conditions
    // The server refresh above will get the latest data including this transcription
  }
}
