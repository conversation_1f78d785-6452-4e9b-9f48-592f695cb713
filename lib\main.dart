import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tray_manager/tray_manager.dart';
import 'package:window_manager/window_manager.dart';
import 'providers/transcription_provider.dart';
import 'providers/auth_provider.dart';
import 'providers/history_provider.dart';
import 'providers/settings_provider.dart';
import 'screens/main_screen.dart';
import 'screens/login_screen.dart';
import 'screens/plans_billing_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await windowManager.ensureInitialized();

  WindowOptions windowOptions = const WindowOptions(
    size: Size(1350, 850),
    center: true,
    backgroundColor: Colors.transparent,
    skipTaskbar: false,
    titleBarStyle: TitleBarStyle.hidden,
  );
  windowManager.waitUntilReadyToShow(windowOptions, () async {
    await windowManager.show();
    await windowManager.focus();
  });

  runApp(const MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> with WindowListener, TrayListener {
  @override
  void initState() {
    super.initState();
    windowManager.addListener(this);
    trayManager.addListener(this);
    _initTray();
  }

  Future<void> _initTray() async {
    await trayManager.setIcon('windows/runner/resources/app_icon.ico');
    Menu menu = Menu(
      items: [
        MenuItem(key: 'show_window', label: 'Show Window'),
        MenuItem.separator(),
        MenuItem(key: 'exit_app', label: 'Exit App'),
      ],
    );
    await trayManager.setContextMenu(menu);
    await trayManager.setToolTip("Murmur AI");
  }

  @override
  void dispose() {
    windowManager.removeListener(this);
    trayManager.removeListener(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => AuthProvider()),
        ChangeNotifierProvider(create: (context) => TranscriptionProvider()),
        ChangeNotifierProvider(create: (context) => HistoryProvider()),
        ChangeNotifierProvider(create: (context) => SettingsProvider()),
      ],
      child: Consumer<SettingsProvider>(
        builder: (context, settings, child) {
          return MaterialApp(
            title: 'MurMur AI',
            themeMode: settings.themeMode,
            theme: ThemeData(
              colorScheme: const ColorScheme.light(
                primary: Colors.blue,
                onPrimary: Colors.white,
                primaryContainer: Color(0xFFF0F4FF),
                onPrimaryContainer: Color(0xFF1A1A1A),
                secondary: Colors.blue,
                onSecondary: Colors.white,
                secondaryContainer: Color(0xFFF0F4FF),
                onSecondaryContainer: Color(0xFF1A1A1A),
                surface: Color(0xFFFAFAFA),
                onSurface: Color(0xFF1A1A1A),
                surfaceContainerLow: Color(0xFFFFFFFF),
                surfaceContainerHigh: Color(0xFFF5F5F5),
                onSurfaceVariant: Color(0xFF666666),
                outline: Color(0xFFE0E0E0),
                error: Colors.red,
                onError: Colors.white,
              ),
              fontFamily: 'SF Pro Display', // Modern system font
              textTheme: const TextTheme(
                // Large headers like "Welcome back, nithin"
                displayLarge: TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.w700, // Bold
                  letterSpacing: -0.5,
                  height: 1.2,
                ),
                displayMedium: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.w700,
                  letterSpacing: -0.3,
                  height: 1.3,
                ),
                displaySmall: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.w700,
                  letterSpacing: -0.2,
                  height: 1.3,
                ),
                // Section headers
                headlineLarge: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.w600, // Semi-bold
                  letterSpacing: -0.2,
                  height: 1.3,
                ),
                headlineMedium: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  letterSpacing: -0.1,
                  height: 1.3,
                ),
                headlineSmall: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  letterSpacing: 0,
                  height: 1.4,
                ),
                // Navigation items, important UI elements
                titleLarge: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500, // Medium
                  letterSpacing: 0,
                  height: 1.4,
                ),
                titleMedium: TextStyle(
                  fontSize: 15,
                  fontWeight: FontWeight.w500,
                  letterSpacing: 0.1,
                  height: 1.4,
                ),
                titleSmall: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  letterSpacing: 0.1,
                  height: 1.4,
                ),
                // Regular body text
                bodyLarge: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w400, // Regular
                  letterSpacing: 0,
                  height: 1.5,
                ),
                bodyMedium: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                  letterSpacing: 0.1,
                  height: 1.5,
                ),
                bodySmall: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  letterSpacing: 0.2,
                  height: 1.5,
                ),
                // Button text
                labelLarge: TextStyle(
                  fontSize: 15,
                  fontWeight: FontWeight.w500,
                  letterSpacing: 0.1,
                  height: 1.2,
                ),
                labelMedium: TextStyle(
                  fontSize: 13,
                  fontWeight: FontWeight.w500,
                  letterSpacing: 0.2,
                  height: 1.2,
                ),
                // Timestamps, metadata
                labelSmall: TextStyle(
                  fontSize: 11,
                  fontWeight: FontWeight.w400,
                  letterSpacing: 0.3,
                  height: 1.3,
                ),
              ),
              useMaterial3: true,
              visualDensity: VisualDensity.adaptivePlatformDensity,
            ),
            darkTheme: ThemeData(
              colorScheme: const ColorScheme.dark(
                primary: Colors.blue,
                onPrimary: Colors.white,
                primaryContainer: Color(0xFF3A3A3A),
                onPrimaryContainer: Colors.white,
                secondary: Colors.blue,
                onSecondary: Colors.white,
                secondaryContainer: Color(0xFF3A3A3A),
                onSecondaryContainer: Colors.white,
                surface: Color(0xFF1A1A1A),
                onSurface: Colors.white,
                surfaceContainerLow: Color(0xFF2A2A2A),
                surfaceContainerHigh: Color(0xFF3A3A3A),
                onSurfaceVariant: Color(0xFFB3B3B3),
                outline: Color(0xFF3A3A3A),
                error: Colors.red,
                onError: Colors.white,
              ),
              fontFamily: 'SF Pro Display', // Modern system font
              textTheme: const TextTheme(
                // Large headers like "Welcome back, nithin"
                displayLarge: TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.w700, // Bold
                  letterSpacing: -0.5,
                  height: 1.2,
                ),
                displayMedium: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.w700,
                  letterSpacing: -0.3,
                  height: 1.3,
                ),
                displaySmall: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.w700,
                  letterSpacing: -0.2,
                  height: 1.3,
                ),
                // Section headers
                headlineLarge: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.w600, // Semi-bold
                  letterSpacing: -0.2,
                  height: 1.3,
                ),
                headlineMedium: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  letterSpacing: -0.1,
                  height: 1.3,
                ),
                headlineSmall: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  letterSpacing: 0,
                  height: 1.4,
                ),
                // Navigation items, important UI elements
                titleLarge: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500, // Medium
                  letterSpacing: 0,
                  height: 1.4,
                ),
                titleMedium: TextStyle(
                  fontSize: 15,
                  fontWeight: FontWeight.w500,
                  letterSpacing: 0.1,
                  height: 1.4,
                ),
                titleSmall: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  letterSpacing: 0.1,
                  height: 1.4,
                ),
                // Regular body text
                bodyLarge: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w400, // Regular
                  letterSpacing: 0,
                  height: 1.5,
                ),
                bodyMedium: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                  letterSpacing: 0.1,
                  height: 1.5,
                ),
                bodySmall: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  letterSpacing: 0.2,
                  height: 1.5,
                ),
                // Button text
                labelLarge: TextStyle(
                  fontSize: 15,
                  fontWeight: FontWeight.w500,
                  letterSpacing: 0.1,
                  height: 1.2,
                ),
                labelMedium: TextStyle(
                  fontSize: 13,
                  fontWeight: FontWeight.w500,
                  letterSpacing: 0.2,
                  height: 1.2,
                ),
                // Timestamps, metadata
                labelSmall: TextStyle(
                  fontSize: 11,
                  fontWeight: FontWeight.w400,
                  letterSpacing: 0.3,
                  height: 1.3,
                ),
              ),
              useMaterial3: true,
              visualDensity: VisualDensity.adaptivePlatformDensity,
            ),
            home: const AuthWrapper(),
            routes: {'/plans': (context) => const PlansBillingScreen()},
            debugShowCheckedModeBanner: false,
          );
        },
      ),
    );
  }

  @override
  void onWindowClose() {
    windowManager.hide();
  }

  @override
  void onTrayIconMouseDown() {
    windowManager.show();
  }

  @override
  void onTrayMenuItemClick(MenuItem menuItem) {
    if (menuItem.key == 'show_window') {
      windowManager.show();
    } else if (menuItem.key == 'exit_app') {
      windowManager.destroy();
    }
  }
}

class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  @override
  void initState() {
    super.initState();
    // Initialize authentication state and global hotkey
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final transcriptionProvider = Provider.of<TranscriptionProvider>(
        context,
        listen: false,
      );
      final historyProvider = Provider.of<HistoryProvider>(
        context,
        listen: false,
      );
      final settingsProvider = Provider.of<SettingsProvider>(
        context,
        listen: false,
      );

      // Set up provider connections for credit and history updates
      transcriptionProvider.setAuthProvider(authProvider);
      transcriptionProvider.setHistoryProvider(historyProvider);
      transcriptionProvider.setSettingsProvider(settingsProvider);

      // Initialize services
      authProvider.initializeAuth();
      transcriptionProvider.initializeGlobalHotkey();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        switch (authProvider.state) {
          case AuthState.initial:
          case AuthState.loading:
            return const Scaffold(
              body: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.mic, size: 80, color: Colors.blue),
                    SizedBox(height: 24),
                    Text(
                      'MurMur AI',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue,
                      ),
                    ),
                    SizedBox(height: 32),
                    CircularProgressIndicator(),
                  ],
                ),
              ),
            );
          case AuthState.authenticated:
            return const MainScreen();
          case AuthState.unauthenticated:
          case AuthState.error:
            return const LoginScreen();
        }
      },
    );
  }
}
