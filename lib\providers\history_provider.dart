import 'package:flutter/foundation.dart';
import '../models/transcription_history.dart';
import '../services/transcription_api_service.dart';
import '../services/history_cache_service.dart';
import 'dart:async';

class HistoryProvider extends ChangeNotifier {
  List<TranscriptionHistoryItem> _historyItems = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  String _errorMessage = '';
  int _total = 0;
  int _currentSkip = 0;
  bool _hasMoreItems = true;
  static const int _limit = 20;

  // Cache state tracking
  bool _isLoadingFromCache = false;
  bool _hasCache = false;
  DateTime? _lastCacheTime;

  // Background refresh timer
  Timer? _backgroundRefreshTimer;

  // Constructor - initialize background refresh
  HistoryProvider() {
    initializeBackgroundRefresh();
  }

  // Getters
  List<TranscriptionHistoryItem> get historyItems => _historyItems;
  bool get isLoading => _isLoading;
  bool get isLoadingMore => _isLoadingMore;
  bool get isLoadingFromCache => _isLoadingFromCache;
  String get errorMessage => _errorMessage;
  int get totalItems => _total;
  bool get hasMoreItems => _hasMoreItems;
  bool get hasCache => _hasCache;
  DateTime? get lastCacheTime => _lastCacheTime;

  /// Load history with cache-first strategy
  Future<void> loadHistory({
    bool refresh = false,
    bool forceServer = false,
    bool afterTranscription = false,
  }) async {
    print(
      '📋 Loading history (refresh: $refresh, forceServer: $forceServer, afterTranscription: $afterTranscription)',
    );

    // If this is after a transcription, force a complete server refresh
    if (afterTranscription) {
      print(
        '🔄 After transcription: Forcing complete server refresh for latest data',
      );
      await _loadFromServer(refresh: true);
      return;
    }

    // Always try cache first unless explicitly forcing server
    if (!forceServer) {
      await _loadFromCacheFirst();

      // Check if we have any processing items in cache
      final hasProcessingItems = _historyItems.any((item) => item.isProcessing);

      // If we have cache data and this is not after a transcription, use cache
      // BUT always refresh from server if we have processing items to get updated status
      if (_historyItems.isNotEmpty && !hasProcessingItems) {
        print('✅ Using cached data, no server request needed');
        return;
      }

      // Only refresh from server if:
      // 1. Cache is completely empty, OR
      // 2. We have processing items that need status updates, OR
      // 3. Explicitly forcing a refresh AND cache is older than 1 hour
      bool shouldRefreshFromServer =
          _historyItems.isEmpty ||
          hasProcessingItems ||
          (refresh &&
              _lastCacheTime != null &&
              DateTime.now().difference(_lastCacheTime!).inHours >= 1);

      if (!shouldRefreshFromServer) {
        print(
          '✅ Cache available and no processing items, skipping server request',
        );
        _errorMessage = ''; // Clear any previous errors
        notifyListeners();
        return;
      }

      if (hasProcessingItems) {
        print(
          '🔄 Found processing items in cache, refreshing from server to get latest status',
        );
      }
    }

    // Only load from server if absolutely necessary
    await _loadFromServer(refresh: refresh);
  }

  /// Load from cache first, then optionally from server
  Future<void> _loadFromCacheFirst() async {
    _isLoadingFromCache = true;
    _errorMessage = '';
    notifyListeners();

    try {
      final cacheResult = await HistoryCacheService.loadFromCache();

      if (cacheResult != null && cacheResult.items.isNotEmpty) {
        _historyItems = cacheResult.items;
        _total = cacheResult.totalCount;
        _hasCache = true;
        _lastCacheTime = DateTime.now().subtract(cacheResult.cacheAge);
        _hasMoreItems = false; // Cache contains all fetched items
        _currentSkip = cacheResult.items.length;

        print('📥 Loaded ${cacheResult.items.length} items from cache');

        // If cache is expired, refresh in background
        if (cacheResult.isExpired) {
          print('⏰ Cache expired, refreshing in background');
          _refreshInBackground();
        }
      } else {
        print('📭 No cache available');
      }
    } catch (e) {
      print('❌ Failed to load from cache: $e');
    }

    _isLoadingFromCache = false;
    notifyListeners();
  }

  /// Load from server with optional refresh
  Future<void> _loadFromServer({bool refresh = false}) async {
    try {
      _isLoading = true;
      _errorMessage = '';
      if (!refresh) notifyListeners();

      print('🌐 Loading from server (skip: $_currentSkip, limit: $_limit)');

      final result = await TranscriptionApiService.getTranscriptionHistory(
        skip: refresh ? 0 : _currentSkip,
        limit: _limit,
      );

      if (!result['success']) {
        throw Exception(result['error']);
      }

      final response = TranscriptionHistoryResponse.fromJson(result['data']);

      // Debug: Log the parsed response data
      print(
        '🔍 Parsed ${response.transcriptions.length} transcriptions from API',
      );
      for (int i = 0; i < response.transcriptions.length && i < 3; i++) {
        final item = response.transcriptions[i];
        final textLength = item.text?.length ?? 0;
        final wordCount = item.wordCount ?? 0;
        print(
          '🔍 Parsed item $i: text_length=$textLength, word_count=$wordCount, id=${item.transcriptionId}',
        );
        if (item.text != null && item.text!.isNotEmpty) {
          print(
            '🔍 Parsed item $i text preview: "${item.text!.substring(0, item.text!.length > 100 ? 100 : item.text!.length)}..."',
          );
        }
      }

      if (refresh) {
        _historyItems = response.transcriptions;
        _currentSkip = 0;
      } else {
        _historyItems.addAll(response.transcriptions);
      }

      _total = response.total;
      _currentSkip += response.transcriptions.length;
      _hasMoreItems = _historyItems.length < _total;

      // Update cache after successful server load
      await _updateCache(refresh);

      print(
        '✅ Server data loaded: ${_historyItems.length}/${_total} items, hasMore: $_hasMoreItems',
      );
    } catch (e) {
      _errorMessage = e.toString().replaceFirst('Exception: ', '');
      print('❌ Failed to load from server: $_errorMessage');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Refresh history from server
  Future<void> refreshHistory() async {
    await loadHistory(refresh: true, forceServer: true);
  }

  /// Load more items (pagination)
  Future<void> loadMoreHistory() async {
    if (!_hasMoreItems || _isLoadingMore) return;
    await _loadFromServer(refresh: false);
  }

  /// Add new transcription to cache and local state
  Future<void> addNewTranscription(TranscriptionHistoryItem newItem) async {
    try {
      // Add to cache
      await HistoryCacheService.prependToCache(newItem);

      // Add to local state
      _historyItems.insert(0, newItem);
      _total += 1;

      print('➕ Added new transcription to history: ${newItem.transcriptionId}');
      notifyListeners();
    } catch (e) {
      print('❌ Failed to add new transcription: $e');
    }
  }

  /// Update existing transcription in cache and local state
  Future<void> updateTranscription(TranscriptionHistoryItem updatedItem) async {
    try {
      // Update in cache
      await HistoryCacheService.updateInCache(updatedItem);

      // Update in local state
      final index = _historyItems.indexWhere(
        (item) => item.transcriptionId == updatedItem.transcriptionId,
      );

      if (index >= 0) {
        _historyItems[index] = updatedItem;
        print(
          '🔄 Updated transcription in history: ${updatedItem.transcriptionId}',
        );
        notifyListeners();
      }
    } catch (e) {
      print('❌ Failed to update transcription: $e');
    }
  }

  /// Refresh cache in background
  Future<void> _refreshInBackground() async {
    try {
      print('🔄 Background refresh started');
      final result = await TranscriptionApiService.getTranscriptionHistory(
        skip: 0,
        limit: _historyItems.length.clamp(
          20,
          100,
        ), // Fetch at least what we have
      );

      if (result['success']) {
        final response = TranscriptionHistoryResponse.fromJson(result['data']);

        // Update cache without disturbing UI
        await HistoryCacheService.saveToCache(
          items: response.transcriptions,
          totalCount: response.total,
        );

        // Only update UI if there are actual changes
        if (response.transcriptions.length != _historyItems.length ||
            response.total != _total) {
          _historyItems = response.transcriptions;
          _total = response.total;
          _lastCacheTime = DateTime.now();
          notifyListeners();
          print('🔄 Background refresh completed with changes');
        } else {
          print('🔄 Background refresh completed - no changes');
        }
      }
    } catch (e) {
      print('❌ Background refresh failed: $e');
    }
  }

  /// Clear cache and reload
  Future<void> clearCacheAndReload() async {
    await HistoryCacheService.clearCache();
    _hasCache = false;
    _lastCacheTime = null;
    await loadHistory(refresh: true, forceServer: true);
  }

  /// Get cache information for debugging
  Future<String> getCacheInfo() async {
    return await HistoryCacheService.getCacheInfo();
  }

  // Property to check if the error is due to rate limiting
  bool get isRateLimited =>
      _errorMessage.toLowerCase().contains('server is busy') ||
      _errorMessage.toLowerCase().contains('rate limit');

  // Start background refresh for processing items
  void _startBackgroundRefreshForProcessingItems() {
    // Cancel existing timer if any
    _backgroundRefreshTimer?.cancel();

    // Check every 30 seconds for processing items and refresh if found
    _backgroundRefreshTimer = Timer.periodic(Duration(seconds: 30), (
      timer,
    ) async {
      if (_historyItems.any((item) => item.isProcessing)) {
        print(
          '🔄 Background refresh: Found processing items, refreshing from server',
        );
        await _loadFromServer(refresh: true);
      }
    });
  }

  // Initialize background refresh when provider is created
  void initializeBackgroundRefresh() {
    _startBackgroundRefreshForProcessingItems();
  }

  // Override dispose to clean up timers
  @override
  void dispose() {
    _backgroundRefreshTimer?.cancel();
    super.dispose();
  }

  // Update cache after successful server load
  Future<void> _updateCache(bool refresh) async {
    try {
      await HistoryCacheService.saveToCache(
        items: _historyItems,
        totalCount: _total,
      );
      _hasCache = true;
      _lastCacheTime = DateTime.now();
      print('💾 Cache updated with ${_historyItems.length} items');
    } catch (e) {
      print('⚠️ Failed to update cache: $e');
    }
  }
}
